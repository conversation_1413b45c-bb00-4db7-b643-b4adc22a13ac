# Amazing Deals - Production Deployment Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Server Setup](#server-setup)
3. [Database Configuration](#database-configuration)
4. [Application Deployment](#application-deployment)
5. [Web Server Configuration](#web-server-configuration)
6. [SSL Certificate Setup](#ssl-certificate-setup)
7. [Process Management](#process-management)
8. [Security Configuration](#security-configuration)
9. [Monitoring & Logging](#monitoring--logging)
10. [Backup Strategy](#backup-strategy)
11. [Performance Optimization](#performance-optimization)
12. [Troubleshooting](#troubleshooting)
13. [Maintenance](#maintenance)

## Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04/22.04 LTS
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores recommended
- **Network**: Public IP address and domain name

### Required Services
- Node.js 18+ LTS
- PostgreSQL 12+
- Nginx
- PM2 Process Manager
- SSL Certificate (Let's Encrypt)

## Server Setup

### 1. Initial Server Configuration

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git nginx postgresql postgresql-contrib \
    software-properties-common apt-transport-https ca-certificates \
    gnupg lsb-release ufw fail2ban

# Create application user
sudo adduser --system --group --home /var/www/amazing-deals amazing-deals
sudo usermod -aG sudo amazing-deals
```

### 2. Node.js Installation

```bash
# Install Node.js LTS (using NodeSource repository)
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version  # Should be 18.x or higher
npm --version   # Should be 9.x or higher

# Install PM2 globally
sudo npm install -g pm2
```

### 3. System Security Setup

```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS

# Configure fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Disable root login and password authentication (recommended)
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

## Database Configuration

### 1. PostgreSQL Setup

```bash
# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << 'EOF'
CREATE DATABASE amazing_deals;
CREATE USER amazing_deals_user WITH PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE amazing_deals TO amazing_deals_user;
ALTER USER amazing_deals_user CREATEDB;
\q
EOF
```

### 2. Database Security

```bash
# Configure PostgreSQL for security
sudo tee -a /etc/postgresql/*/main/postgresql.conf << 'EOF'

# Security settings
ssl = on
password_encryption = scram-sha-256
log_connections = on
log_disconnections = on
log_statement = 'mod'
EOF

# Restart PostgreSQL
sudo systemctl restart postgresql

# Test connection
psql -h localhost -U amazing_deals_user -d amazing_deals -c "SELECT version();"
```

## Application Deployment

### 1. Application Setup

```bash
# Switch to application user
sudo su - amazing-deals

# Clone repository (or upload files)
cd /var/www/amazing-deals
git clone https://github.com/your-username/amazing-deals.git .
# OR: Upload files via SCP/SFTP

# Install dependencies
npm ci --only=production

# Create production environment file
cat > .env.local << 'EOF'
# Database Configuration
DATABASE_URL="postgresql://amazing_deals_user:aydha@6004@localhost:5432/amazing_deals"

# Authentication
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="your_secure_admin_password"
JWT_SECRET="$(openssl rand -base64 32)"

# Next.js Configuration
NEXTAUTH_SECRET="$(openssl rand -base64 32)"
NEXTAUTH_URL="https://your-domain.com"
NODE_ENV="production"

# Communication Integrations
TELEGRAM_BOT_TOKEN="your_telegram_bot_token"
TELEGRAM_CHANNEL_ID="@your_channel"
TELEGRAM_NOTIFICATIONS_ENABLED="true"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="Amazing Deals <<EMAIL>>"

# WhatsApp Configuration
WHATSAPP_BUSINESS_ACCOUNT_ID="your_account_id"
WHATSAPP_ACCESS_TOKEN="your_access_token"
WHATSAPP_PHONE_NUMBER_ID="your_phone_number_id"
WHATSAPP_WEBHOOK_VERIFY_TOKEN="your_verify_token"

# Amazon Affiliate
AMAZON_AFFILIATE_TAG="shaf0bb-21"
EOF

# Set secure permissions
chmod 600 .env.local
chown amazing-deals:amazing-deals .env.local
```

### 2. Database Migration

```bash
# Run database migrations
psql -h localhost -U amazing_deals_user -d amazing_deals << 'EOF'
-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(500) NOT NULL,
    description TEXT,
    amazon_affiliate_link TEXT NOT NULL,
    image_url TEXT,
    price DECIMAL(10,2),
    mrp DECIMAL(10,2),
    discount_percentage INTEGER,
    category_id UUID REFERENCES categories(id),
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create banners table
CREATE TABLE IF NOT EXISTS banners (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    image_url TEXT NOT NULL,
    link_url TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create email subscribers table
CREATE TABLE IF NOT EXISTS email_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    subscribed BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create email campaigns table
CREATE TABLE IF NOT EXISTS email_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create WhatsApp subscribers table
CREATE TABLE IF NOT EXISTS whatsapp_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(255),
    subscribed BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO categories (name, description) VALUES 
    ('Electronics', 'Electronic gadgets and devices'),
    ('Fashion', 'Clothing and accessories'),
    ('Home & Kitchen', 'Home appliances and kitchen items')
ON CONFLICT (name) DO NOTHING;

-- Insert sample banners
INSERT INTO banners (title, image_url, link_url) VALUES 
    ('Amazing Electronics Deals', 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=400&fit=crop', 'https://amazon.in'),
    ('Fashion Sale', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=1200&h=400&fit=crop', 'https://amazon.in'),
    ('Home & Kitchen Essentials', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=1200&h=400&fit=crop', 'https://amazon.in')
ON CONFLICT DO NOTHING;
EOF
```

### 3. Build Application

```bash
# Build the Next.js application
npm run build

# Test the build
npm start &
sleep 10
curl http://localhost:3000/api/products
kill %1
```

## Web Server Configuration

### 1. Nginx Setup

```bash
# Create Nginx configuration
sudo tee /etc/nginx/sites-available/amazing-deals << 'EOF'
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

# Upstream for load balancing
upstream amazing_deals_app {
    server 127.0.0.1:3000;
    keepalive 64;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration (will be configured by Certbot)
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate limiting
    location /api/auth/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://amazing_deals_app;
        include /etc/nginx/proxy_params;
    }

    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://amazing_deals_app;
        include /etc/nginx/proxy_params;
    }

    # Static files caching
    location /_next/static/ {
        alias /var/www/amazing-deals/.next/static/;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }

    # Images and assets
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # Main application
    location / {
        proxy_pass http://amazing_deals_app;
        include /etc/nginx/proxy_params;
        proxy_read_timeout 86400;
    }

    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Create proxy params
sudo tee /etc/nginx/proxy_params << 'EOF'
proxy_set_header Host $http_host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_http_version 1.1;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection "upgrade";
proxy_cache_bypass $http_upgrade;
EOF

# Enable site and remove default
sudo ln -s /etc/nginx/sites-available/amazing-deals /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test and restart Nginx
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## SSL Certificate Setup

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run

# Setup automatic renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## Process Management

### 1. PM2 Configuration

```bash
# Switch to application user
sudo su - amazing-deals

# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'amazing-deals',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/amazing-deals',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/amazing-deals/error.log',
    out_file: '/var/log/amazing-deals/out.log',
    log_file: '/var/log/amazing-deals/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
}
EOF

# Create log directory
sudo mkdir -p /var/log/amazing-deals
sudo chown amazing-deals:amazing-deals /var/log/amazing-deals

# Start application
pm2 start ecosystem.config.js
pm2 save

# Setup PM2 startup
pm2 startup
# Follow the instructions provided by the command
```

### 2. Systemd Service (Alternative)

```bash
# Create systemd service file
sudo tee /etc/systemd/system/amazing-deals.service << 'EOF'
[Unit]
Description=Amazing Deals Application
After=network.target postgresql.service

[Service]
Type=simple
User=amazing-deals
WorkingDirectory=/var/www/amazing-deals
Environment=NODE_ENV=production
Environment=PORT=3000
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=amazing-deals

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable amazing-deals
sudo systemctl start amazing-deals
```

## Security Configuration

### 1. Database Security

```bash
# Configure PostgreSQL security
sudo tee -a /etc/postgresql/*/main/pg_hba.conf << 'EOF'
# Amazing Deals application access
local   amazing_deals   amazing_deals_user                  scram-sha-256
host    amazing_deals   amazing_deals_user  127.0.0.1/32   scram-sha-256
EOF

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### 2. Application Security

```bash
# Set proper file permissions
sudo chown -R amazing-deals:amazing-deals /var/www/amazing-deals
sudo chmod -R 755 /var/www/amazing-deals
sudo chmod 600 /var/www/amazing-deals/.env.local

# Secure log files
sudo chmod 755 /var/log/amazing-deals
sudo chmod 644 /var/log/amazing-deals/*.log
```

## Monitoring & Logging

### 1. System Monitoring

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Create monitoring script
sudo tee /usr/local/bin/amazing-deals-monitor.sh << 'EOF'
#!/bin/bash

LOG_FILE="/var/log/amazing-deals/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check application status
if ! pm2 list | grep -q "amazing-deals.*online"; then
    echo "[$DATE] ERROR: Application is down, restarting..." >> $LOG_FILE
    sudo -u amazing-deals pm2 restart amazing-deals
    echo "[$DATE] INFO: Application restarted" >> $LOG_FILE
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] WARNING: Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEMORY_USAGE -gt 80 ]; then
    echo "[$DATE] WARNING: Memory usage is ${MEMORY_USAGE}%" >> $LOG_FILE
fi

# Check database connectivity
if ! sudo -u amazing-deals psql -h localhost -U amazing_deals_user -d amazing_deals -c "SELECT 1;" > /dev/null 2>&1; then
    echo "[$DATE] ERROR: Database connection failed" >> $LOG_FILE
fi

# Check Nginx status
if ! systemctl is-active --quiet nginx; then
    echo "[$DATE] ERROR: Nginx is down" >> $LOG_FILE
    sudo systemctl restart nginx
fi
EOF

sudo chmod +x /usr/local/bin/amazing-deals-monitor.sh

# Schedule monitoring (every 5 minutes)
echo "*/5 * * * * /usr/local/bin/amazing-deals-monitor.sh" | sudo crontab -
```

### 2. Log Management

```bash
# Configure log rotation
sudo tee /etc/logrotate.d/amazing-deals << 'EOF'
/var/log/amazing-deals/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 amazing-deals amazing-deals
    postrotate
        sudo -u amazing-deals pm2 reloadLogs
    endscript
}
EOF

# Configure rsyslog for application logs
sudo tee /etc/rsyslog.d/50-amazing-deals.conf << 'EOF'
if $programname == 'amazing-deals' then /var/log/amazing-deals/app.log
& stop
EOF

sudo systemctl restart rsyslog
```

## Backup Strategy

### 1. Automated Backups

```bash
# Create backup script
sudo tee /usr/local/bin/amazing-deals-backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/var/backups/amazing-deals"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
echo "Starting database backup..."
sudo -u postgres pg_dump amazing_deals | gzip > $BACKUP_DIR/database_$DATE.sql.gz

# Application files backup
echo "Starting application backup..."
tar -czf $BACKUP_DIR/app_$DATE.tar.gz \
    -C /var/www amazing-deals \
    --exclude=node_modules \
    --exclude=.next \
    --exclude=.git

# Environment backup
cp /var/www/amazing-deals/.env.local $BACKUP_DIR/env_$DATE.backup

# Clean old backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.backup" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $DATE"
EOF

sudo chmod +x /usr/local/bin/amazing-deals-backup.sh

# Schedule daily backups at 2 AM
echo "0 2 * * * /usr/local/bin/amazing-deals-backup.sh" | sudo crontab -
```

### 2. Restore Procedures

```bash
# Database restore example
# gunzip -c /var/backups/amazing-deals/database_YYYYMMDD_HHMMSS.sql.gz | \
#   sudo -u postgres psql amazing_deals

# Application restore example
# sudo systemctl stop amazing-deals
# cd /var/www
# sudo tar -xzf /var/backups/amazing-deals/app_YYYYMMDD_HHMMSS.tar.gz
# sudo systemctl start amazing-deals
```

## Performance Optimization

### 1. PostgreSQL Tuning

```bash
# Calculate optimal settings based on system resources
TOTAL_RAM=$(free -m | awk 'NR==2{print $2}')
SHARED_BUFFERS=$((TOTAL_RAM / 4))
EFFECTIVE_CACHE_SIZE=$((TOTAL_RAM * 3 / 4))

sudo tee -a /etc/postgresql/*/main/postgresql.conf << EOF

# Performance tuning
shared_buffers = ${SHARED_BUFFERS}MB
effective_cache_size = ${EFFECTIVE_CACHE_SIZE}MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
max_worker_processes = 8
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2
EOF

sudo systemctl restart postgresql
```

### 2. System Optimization

```bash
# Optimize system limits
echo 'fs.file-max = 65536' | sudo tee -a /etc/sysctl.conf
echo '* soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65536' | sudo tee -a /etc/security/limits.conf

# Apply changes
sudo sysctl -p

# Optimize network settings
echo 'net.core.somaxconn = 65535' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Application Won't Start

```bash
# Check logs
sudo -u amazing-deals pm2 logs amazing-deals
tail -f /var/log/amazing-deals/error.log

# Check environment variables
sudo -u amazing-deals cat /var/www/amazing-deals/.env.local

# Check database connection
sudo -u amazing-deals psql -h localhost -U amazing_deals_user -d amazing_deals -c "SELECT version();"

# Restart application
sudo -u amazing-deals pm2 restart amazing-deals
```

#### 2. Database Connection Issues

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-*-main.log

# Test connection
sudo -u postgres psql -c "SELECT version();"

# Reset database password
sudo -u postgres psql -c "ALTER USER amazing_deals_user PASSWORD 'new_password';"
```

#### 3. SSL Certificate Issues

```bash
# Check certificate status
sudo certbot certificates

# Renew certificate manually
sudo certbot renew --force-renewal

# Check Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

#### 4. High Memory Usage

```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head

# Restart application to clear memory
sudo -u amazing-deals pm2 restart amazing-deals

# Check for memory leaks
sudo -u amazing-deals pm2 monit
```

#### 5. Slow Performance

```bash
# Check system resources
htop
iotop
nethogs

# Check database performance
sudo -u postgres psql amazing_deals -c "SELECT * FROM pg_stat_activity;"

# Analyze slow queries
sudo -u postgres psql amazing_deals -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Restart services
sudo systemctl restart nginx
sudo -u amazing-deals pm2 restart amazing-deals
```

## Maintenance

### Daily Tasks

```bash
# Check system status
sudo systemctl status nginx postgresql
sudo -u amazing-deals pm2 status

# Check disk space
df -h

# Check logs for errors
sudo tail -n 100 /var/log/amazing-deals/error.log | grep ERROR
```

### Weekly Tasks

```bash
# Update system packages
sudo apt update && sudo apt list --upgradable

# Check backup integrity
ls -la /var/backups/amazing-deals/

# Review monitoring logs
sudo tail -n 500 /var/log/amazing-deals/monitor.log
```

### Monthly Tasks

```bash
# Security updates
sudo apt update && sudo apt upgrade -y

# Clean old logs
sudo find /var/log -name "*.log" -mtime +30 -delete

# Database maintenance
sudo -u postgres psql amazing_deals -c "VACUUM ANALYZE;"

# SSL certificate check
sudo certbot certificates
```

### Application Updates

```bash
# 1. Backup current version
/usr/local/bin/amazing-deals-backup.sh

# 2. Switch to application user
sudo su - amazing-deals

# 3. Pull latest changes
cd /var/www/amazing-deals
git pull origin main

# 4. Install dependencies
npm ci --only=production

# 5. Build application
npm run build

# 6. Restart application
pm2 restart amazing-deals

# 7. Verify deployment
curl -I https://your-domain.com
pm2 logs amazing-deals --lines 50
```

## Security Best Practices

1. **Regular Updates**: Keep system and dependencies updated
2. **Strong Passwords**: Use complex passwords for all accounts
3. **SSH Keys**: Use SSH key authentication instead of passwords
4. **Firewall**: Configure UFW to allow only necessary ports
5. **SSL/TLS**: Use strong SSL certificates and configurations
6. **Backups**: Maintain regular, tested backups
7. **Monitoring**: Implement comprehensive monitoring and alerting
8. **Logs**: Regularly review logs for suspicious activity
9. **Database**: Secure database with proper user permissions
10. **Environment**: Keep sensitive data in environment variables

## Support and Resources

- **Application Logs**: `/var/log/amazing-deals/`
- **Nginx Logs**: `/var/log/nginx/`
- **PostgreSQL Logs**: `/var/log/postgresql/`
- **System Logs**: `/var/log/syslog`
- **PM2 Monitoring**: `pm2 monit`
- **System Monitoring**: `htop`, `iotop`, `nethogs`

For additional support, check the application documentation and logs for specific error messages.
```
