# Amazon Affiliate Store

A full-stack Amazon Affiliate web application built with Next.js, TypeScript, Prisma, and PostgreSQL.

## Features

- **User Authentication**: JWT-based authentication with admin and user roles
- **Admin Dashboard**: Manage categories and products with full CRUD operations
- **Public Store**: Responsive product catalog with search and filtering
- **Database**: PostgreSQL with Prisma ORM
- **Validation**: Form validation using Zod
- **Responsive Design**: Mobile-friendly interface with TailwindCSS

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, TailwindCSS
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with bcrypt password hashing
- **Validation**: Zod
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database (or use Prisma's local dev database)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd reactaff
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` with your database URL and JWT secret:
```env
DATABASE_URL="your-postgresql-connection-string"
JWT_SECRET="your-super-secret-jwt-key"
NEXTAUTH_URL="http://localhost:3000"
```

4. Set up the database:
```bash
# Generate Prisma client
npx prisma generate

# Push database schema
npx prisma db push

# Seed the database
npm run db:seed
```

5. Start the development server:
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## Admin Access

Default admin credentials:
- Email: `<EMAIL>`
- Password: `admin123`

Access the admin dashboard at `/admin`

## Database Schema

### Users
- `id`: Unique identifier
- `name`: User's full name
- `email`: User's email (unique)
- `passwordHash`: Hashed password
- `role`: User role (ADMIN/USER)

### Categories
- `id`: Unique identifier
- `name`: Category name (unique)
- `slug`: URL-friendly slug (unique)

### Products
- `id`: Unique identifier
- `name`: Product name
- `description`: Product description
- `amazonAffiliateLink`: Amazon affiliate URL
- `imageUrl`: Product image URL
- `price`: Product price
- `categoryId`: Foreign key to categories
- `createdById`: Foreign key to users

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Categories (Public)
- `GET /api/categories` - Get all categories
- `GET /api/categories/[id]` - Get single category

### Categories (Admin)
- `POST /api/categories` - Create category
- `PUT /api/categories/[id]` - Update category
- `DELETE /api/categories/[id]` - Delete category

### Products (Public)
- `GET /api/products` - Get products with filtering
- `GET /api/products/[id]` - Get single product
- `GET /api/search` - Search products

### Products (Admin)
- `POST /api/products` - Create product
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Delete product

## Deployment

### Vercel Deployment

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard:
   - `DATABASE_URL`
   - `JWT_SECRET`
   - `NEXTAUTH_URL`
4. Deploy!

### Database Setup for Production

For production, you can use:
- **Vercel Postgres**: Built-in PostgreSQL database
- **Supabase**: Free PostgreSQL with additional features
- **Railway**: Simple PostgreSQL hosting
- **Neon**: Serverless PostgreSQL

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:seed` - Seed database with sample data

## License

This project is licensed under the MIT License.

## Disclaimer

This application is for educational purposes. Make sure to comply with Amazon's affiliate program terms and conditions when using affiliate links.
