-- Add MRP and discount fields to products table
-- Run this migration to add the new pricing fields

-- Add MRP (Maximum Retail Price) field
ALTER TABLE products 
ADD COLUMN mrp DECIMAL(10,2) NULL;

-- Add discount percentage field
ALTER TABLE products 
ADD COLUMN discount_percentage INTEGER NULL;

-- Add comments for documentation
COMMENT ON COLUMN products.mrp IS 'Maximum Retail Price - the original price before discount';
COMMENT ON COLUMN products.discount_percentage IS 'Discount percentage (0-100) - calculated or manually set';

-- Create index for better query performance on price-related searches
CREATE INDEX idx_products_pricing ON products(price, mrp, discount_percentage);

-- Update existing products with sample MRP data (20% higher than current price)
UPDATE products 
SET mrp = ROUND(price * 1.2, 2)
WHERE price IS NOT NULL AND mrp IS NULL;

-- Calculate discount percentage for existing products
UPDATE products 
SET discount_percentage = ROUND(((mrp - price) / mrp) * 100)
WHERE price IS NOT NULL AND mrp IS NOT NULL AND mrp > price;

-- Verify the changes
SELECT 
    id, 
    name, 
    price, 
    mrp, 
    discount_percentage,
    CASE 
        WHEN mrp IS NOT NULL AND price IS NOT NULL AND mrp > price 
        THEN ROUND(((mrp - price) / mrp) * 100)
        ELSE 0 
    END as calculated_discount
FROM products 
WHERE price IS NOT NULL 
LIMIT 5;
