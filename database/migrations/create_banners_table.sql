-- Create banners table for banner management system
-- Run this migration to create the banners table

CREATE TABLE IF NOT EXISTS banners (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    image_url TEXT NOT NULL,
    link_url TEXT,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) REFERENCES users(id)
);

-- Create indexes for better performance
CREATE INDEX idx_banners_active ON banners(is_active);
CREATE INDEX idx_banners_dates ON banners(start_date, end_date);
CREATE INDEX idx_banners_order ON banners(display_order);
CREATE INDEX idx_banners_created_by ON banners(created_by);

-- Add comments for documentation
COMMENT ON TABLE banners IS 'Banner management for homepage carousel';
COMMENT ON COLUMN banners.id IS 'Unique banner identifier';
COMMENT ON COLUMN banners.title IS 'Banner title/description';
COMMENT ON COLUMN banners.image_url IS 'URL to banner image';
COMMENT ON COLUMN banners.link_url IS 'Optional URL to redirect when banner is clicked';
COMMENT ON COLUMN banners.start_date IS 'When banner becomes active';
COMMENT ON COLUMN banners.end_date IS 'When banner expires (NULL = never expires)';
COMMENT ON COLUMN banners.is_active IS 'Whether banner is currently active';
COMMENT ON COLUMN banners.display_order IS 'Order in which banners are displayed (lower = first)';

-- Insert sample banners
INSERT INTO banners (id, title, image_url, link_url, display_order, created_by) VALUES
('banner_1', 'Welcome to Amazing Deals', 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=400&fit=crop', '#', 1, 'admin_user_001'),
('banner_2', 'Best Electronics Deals', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=1200&h=400&fit=crop', '#electronics', 2, 'admin_user_001'),
('banner_3', 'Home & Garden Sale', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=1200&h=400&fit=crop', '#home-garden', 3, 'admin_user_001');

-- Verify the table creation
SELECT 
    id, 
    title, 
    image_url, 
    is_active, 
    start_date, 
    end_date,
    display_order
FROM banners 
ORDER BY display_order;
