-- Email Marketing System Tables

-- Email subscribers table
CREATE TABLE IF NOT EXISTS email_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHA<PERSON>(255),
    subscribed BOOLEAN DEFAULT true,
    subscription_source VARCHAR(100) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    template_type VARCHAR(50) DEFAULT 'custom',
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email campaigns table
CREATE TABLE IF NOT EXISTS email_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    template_id UUID REFERENCES email_templates(id),
    recipient_count INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    bounced_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email campaign recipients table
CREATE TABLE IF NOT EXISTS email_campaign_recipients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES email_campaigns(id) ON DELETE CASCADE,
    subscriber_id UUID REFERENCES email_subscribers(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    opened_at TIMESTAMP,
    clicked_at TIMESTAMP,
    bounced_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(campaign_id, subscriber_id)
);

-- Email settings table
CREATE TABLE IF NOT EXISTS email_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    smtp_host VARCHAR(255),
    smtp_port INTEGER DEFAULT 587,
    smtp_user VARCHAR(255),
    smtp_password TEXT,
    smtp_secure BOOLEAN DEFAULT true,
    from_email VARCHAR(255),
    from_name VARCHAR(255),
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_subscribers_email ON email_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_email_subscribers_subscribed ON email_subscribers(subscribed);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_status ON email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_email_campaign_recipients_campaign ON email_campaign_recipients(campaign_id);
CREATE INDEX IF NOT EXISTS idx_email_campaign_recipients_status ON email_campaign_recipients(status);

-- Insert default email templates
INSERT INTO email_templates (name, subject, html_content, template_type, is_default) VALUES 
(
    'Product Promotion',
    'Amazing Deals - Special Offers Just for You!',
    '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazing Deals</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #f97316, #ea580c); padding: 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 28px; }
        .content { padding: 30px; }
        .product { border: 1px solid #e5e5e5; border-radius: 8px; margin: 20px 0; padding: 20px; }
        .product img { max-width: 100%; height: auto; border-radius: 4px; }
        .price { color: #16a34a; font-size: 24px; font-weight: bold; }
        .old-price { color: #6b7280; text-decoration: line-through; }
        .discount { background: #dc2626; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .button { background: linear-gradient(135deg, #f97316, #ea580c); color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 0; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Amazing Deals</h1>
            <p style="color: white; margin: 10px 0 0 0;">Your trusted shopping companion</p>
        </div>
        <div class="content">
            <h2>Special Offers Just for You!</h2>
            <p>Hi {{subscriber_name}},</p>
            <p>We have handpicked some amazing deals that we think you will love:</p>
            
            {{products}}
            
            <p>Don''t miss out on these incredible savings!</p>
            <p style="text-align: center;">
                <a href="{{store_url}}" class="button">Shop All Deals</a>
            </p>
        </div>
        <div class="footer">
            <p>© 2024 Amazing Deals. All rights reserved.</p>
            <p>
                <a href="{{unsubscribe_url}}" style="color: #6b7280;">Unsubscribe</a> | 
                <a href="{{store_url}}" style="color: #6b7280;">Visit Store</a>
            </p>
        </div>
    </div>
</body>
</html>',
    'promotion',
    true
),
(
    'Newsletter',
    'Amazing Deals Weekly Newsletter',
    '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazing Deals Newsletter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #f97316, #ea580c); padding: 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 28px; }
        .content { padding: 30px; }
        .section { margin: 30px 0; }
        .section h3 { color: #f97316; border-bottom: 2px solid #f97316; padding-bottom: 10px; }
        .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .product-card { border: 1px solid #e5e5e5; border-radius: 8px; padding: 15px; text-align: center; }
        .product-card img { max-width: 100%; height: 150px; object-fit: cover; border-radius: 4px; }
        .button { background: linear-gradient(135deg, #f97316, #ea580c); color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; display: inline-block; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 Amazing Deals Newsletter</h1>
            <p style="color: white; margin: 10px 0 0 0;">Weekly deals and updates</p>
        </div>
        <div class="content">
            <h2>This Week''s Highlights</h2>
            <p>Hello {{subscriber_name}},</p>
            <p>Here''s what''s trending this week at Amazing Deals:</p>
            
            <div class="section">
                <h3>🔥 Hot Deals</h3>
                {{featured_products}}
            </div>
            
            <div class="section">
                <h3>📱 New Arrivals</h3>
                {{new_products}}
            </div>
            
            <div class="section">
                <h3>💡 Shopping Tips</h3>
                <p>Always check for additional discounts and compare prices before making a purchase. Our affiliate links help support Amazing Deals while getting you the best prices!</p>
            </div>
        </div>
        <div class="footer">
            <p>© 2024 Amazing Deals. All rights reserved.</p>
            <p>
                <a href="{{unsubscribe_url}}" style="color: #6b7280;">Unsubscribe</a> | 
                <a href="{{store_url}}" style="color: #6b7280;">Visit Store</a>
            </p>
        </div>
    </div>
</body>
</html>',
    'newsletter',
    true
)
ON CONFLICT DO NOTHING;

-- Insert default email settings (will need to be configured)
INSERT INTO email_settings (smtp_host, smtp_port, from_email, from_name, is_active) VALUES
('smtp.gmail.com', 587, '<EMAIL>', 'Amazing Deals', false)
ON CONFLICT DO NOTHING;

-- WhatsApp Business Integration Tables

-- WhatsApp subscribers table
CREATE TABLE IF NOT EXISTS whatsapp_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(255),
    subscribed BOOLEAN DEFAULT true,
    subscription_source VARCHAR(100) DEFAULT 'manual',
    opt_in_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    opt_out_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp message templates table
CREATE TABLE IF NOT EXISTS whatsapp_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    template_name VARCHAR(255) NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    category VARCHAR(50) DEFAULT 'MARKETING',
    header_type VARCHAR(20),
    header_text TEXT,
    body_text TEXT NOT NULL,
    footer_text TEXT,
    button_type VARCHAR(20),
    button_text VARCHAR(25),
    button_url TEXT,
    status VARCHAR(50) DEFAULT 'PENDING',
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp campaigns table
CREATE TABLE IF NOT EXISTS whatsapp_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    template_id UUID REFERENCES whatsapp_templates(id),
    template_name VARCHAR(255),
    message_content TEXT NOT NULL,
    recipient_count INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    read_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp campaign recipients table
CREATE TABLE IF NOT EXISTS whatsapp_campaign_recipients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES whatsapp_campaigns(id) ON DELETE CASCADE,
    subscriber_id UUID REFERENCES whatsapp_subscribers(id) ON DELETE CASCADE,
    phone_number VARCHAR(20) NOT NULL,
    message_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    read_at TIMESTAMP,
    failed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(campaign_id, subscriber_id)
);

-- WhatsApp settings table
CREATE TABLE IF NOT EXISTS whatsapp_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_account_id VARCHAR(255),
    access_token TEXT,
    phone_number_id VARCHAR(255),
    webhook_verify_token VARCHAR(255),
    webhook_url VARCHAR(500),
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp message logs table
CREATE TABLE IF NOT EXISTS whatsapp_message_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id VARCHAR(255),
    phone_number VARCHAR(20) NOT NULL,
    message_type VARCHAR(50),
    content TEXT,
    status VARCHAR(50),
    direction VARCHAR(20) DEFAULT 'outbound',
    campaign_id UUID REFERENCES whatsapp_campaigns(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for WhatsApp tables
CREATE INDEX IF NOT EXISTS idx_whatsapp_subscribers_phone ON whatsapp_subscribers(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_subscribers_subscribed ON whatsapp_subscribers(subscribed);
CREATE INDEX IF NOT EXISTS idx_whatsapp_campaigns_status ON whatsapp_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_whatsapp_campaign_recipients_campaign ON whatsapp_campaign_recipients(campaign_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_message_logs_phone ON whatsapp_message_logs(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_message_logs_message_id ON whatsapp_message_logs(message_id);

-- Insert default WhatsApp templates
INSERT INTO whatsapp_templates (name, template_name, body_text, footer_text, category) VALUES
(
    'New Product Alert',
    'new_product_alert',
    'Hi {{1}}! 🛍️ Amazing new deal just arrived!\n\n*{{2}}*\n💰 Price: ₹{{3}}\n🎯 Discount: {{4}}% OFF\n\nDon''t miss out on this incredible offer!',
    'Amazing Deals - Your trusted shopping companion',
    'MARKETING'
),
(
    'Price Drop Alert',
    'price_drop_alert',
    'Great news {{1}}! 📉 Price dropped on a product you might love:\n\n*{{2}}*\n💸 New Price: ₹{{3}}\n💵 You Save: ₹{{4}}\n\nLimited time offer!',
    'Amazing Deals - Your trusted shopping companion',
    'MARKETING'
),
(
    'Daily Deals Summary',
    'daily_deals_summary',
    'Hello {{1}}! 🌟 Here are today''s top deals:\n\n{{2}}\n\nVisit our store for more amazing offers!',
    'Amazing Deals - Your trusted shopping companion',
    'MARKETING'
)
ON CONFLICT DO NOTHING;

-- Insert default WhatsApp settings (will need to be configured)
INSERT INTO whatsapp_settings (business_account_id, is_active) VALUES
('your_business_account_id', false)
ON CONFLICT DO NOTHING;
