-- Complete Database Setup for Amazing Deals Application
-- Run this script to create all required tables and sample data

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(500) NOT NULL,
    description TEXT,
    amazon_affiliate_link TEXT NOT NULL,
    image_url TEXT,
    price DECIMAL(10,2),
    mrp DECIMAL(10,2),
    discount_percentage INTEGER,
    category_id UUID REFERENCES categories(id),
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Banners table
CREATE TABLE IF NOT EXISTS banners (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    image_url TEXT NOT NULL,
    link_url TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email subscribers table
CREATE TABLE IF NOT EXISTS email_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    subscribed BOOLEAN DEFAULT true,
    subscription_source VARCHAR(100) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    template_type VARCHAR(50) DEFAULT 'custom',
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email campaigns table
CREATE TABLE IF NOT EXISTS email_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    template_id UUID REFERENCES email_templates(id),
    recipient_count INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    bounced_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email campaign recipients table
CREATE TABLE IF NOT EXISTS email_campaign_recipients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES email_campaigns(id) ON DELETE CASCADE,
    subscriber_id UUID REFERENCES email_subscribers(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    opened_at TIMESTAMP,
    clicked_at TIMESTAMP,
    bounced_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(campaign_id, subscriber_id)
);

-- Email settings table
CREATE TABLE IF NOT EXISTS email_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    smtp_host VARCHAR(255),
    smtp_port INTEGER DEFAULT 587,
    smtp_user VARCHAR(255),
    smtp_password TEXT,
    smtp_secure BOOLEAN DEFAULT true,
    from_email VARCHAR(255),
    from_name VARCHAR(255),
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp subscribers table
CREATE TABLE IF NOT EXISTS whatsapp_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(255),
    subscribed BOOLEAN DEFAULT true,
    subscription_source VARCHAR(100) DEFAULT 'manual',
    opt_in_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    opt_out_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp message templates table
CREATE TABLE IF NOT EXISTS whatsapp_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    template_name VARCHAR(255) NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    category VARCHAR(50) DEFAULT 'MARKETING',
    header_type VARCHAR(20),
    header_text TEXT,
    body_text TEXT NOT NULL,
    footer_text TEXT,
    button_type VARCHAR(20),
    button_text VARCHAR(25),
    button_url TEXT,
    status VARCHAR(50) DEFAULT 'PENDING',
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp campaigns table
CREATE TABLE IF NOT EXISTS whatsapp_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    template_id UUID REFERENCES whatsapp_templates(id),
    template_name VARCHAR(255),
    message_content TEXT NOT NULL,
    recipient_count INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    read_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp campaign recipients table
CREATE TABLE IF NOT EXISTS whatsapp_campaign_recipients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES whatsapp_campaigns(id) ON DELETE CASCADE,
    subscriber_id UUID REFERENCES whatsapp_subscribers(id) ON DELETE CASCADE,
    phone_number VARCHAR(20) NOT NULL,
    message_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    read_at TIMESTAMP,
    failed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(campaign_id, subscriber_id)
);

-- WhatsApp settings table
CREATE TABLE IF NOT EXISTS whatsapp_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    business_account_id VARCHAR(255),
    access_token TEXT,
    phone_number_id VARCHAR(255),
    webhook_verify_token VARCHAR(255),
    webhook_url VARCHAR(500),
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- WhatsApp message logs table
CREATE TABLE IF NOT EXISTS whatsapp_message_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id VARCHAR(255),
    phone_number VARCHAR(20) NOT NULL,
    message_type VARCHAR(50),
    content TEXT,
    status VARCHAR(50),
    direction VARCHAR(20) DEFAULT 'outbound',
    campaign_id UUID REFERENCES whatsapp_campaigns(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_banners_active ON banners(is_active);
CREATE INDEX IF NOT EXISTS idx_email_subscribers_email ON email_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_email_subscribers_subscribed ON email_subscribers(subscribed);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_status ON email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_email_campaign_recipients_campaign ON email_campaign_recipients(campaign_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_subscribers_phone ON whatsapp_subscribers(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_subscribers_subscribed ON whatsapp_subscribers(subscribed);
CREATE INDEX IF NOT EXISTS idx_whatsapp_campaigns_status ON whatsapp_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_whatsapp_message_logs_phone ON whatsapp_message_logs(phone_number);

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
    ('Electronics', 'Electronic gadgets and devices'),
    ('Fashion', 'Clothing and accessories'),
    ('Home & Kitchen', 'Home appliances and kitchen items'),
    ('Books', 'Books and educational materials'),
    ('Sports & Fitness', 'Sports equipment and fitness gear')
ON CONFLICT (name) DO NOTHING;

-- Insert sample products
INSERT INTO products (name, description, amazon_affiliate_link, image_url, price, mrp, discount_percentage, category_id) 
SELECT 
    'iPhone 15 Pro Max 256GB',
    'Latest iPhone with advanced camera system and titanium design',
    'https://amazon.in/dp/B0CHX1W1XY?tag=shaf0bb-21',
    'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop',
    134900,
    149900,
    10,
    c.id
FROM categories c WHERE c.name = 'Electronics'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, description, amazon_affiliate_link, image_url, price, mrp, discount_percentage, category_id) 
SELECT 
    'Nike Air Max 270 Running Shoes',
    'Comfortable running shoes with Air Max technology',
    'https://amazon.in/dp/B07XVQJZQR?tag=shaf0bb-21',
    'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop',
    8995,
    12995,
    31,
    c.id
FROM categories c WHERE c.name = 'Sports & Fitness'
ON CONFLICT DO NOTHING;

INSERT INTO products (name, description, amazon_affiliate_link, image_url, price, mrp, discount_percentage, category_id) 
SELECT 
    'Instant Pot Duo 7-in-1 Electric Pressure Cooker',
    'Multi-functional pressure cooker for quick and easy cooking',
    'https://amazon.in/dp/B00FLYWNYQ?tag=shaf0bb-21',
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
    7999,
    12999,
    38,
    c.id
FROM categories c WHERE c.name = 'Home & Kitchen'
ON CONFLICT DO NOTHING;

-- Insert sample banners
INSERT INTO banners (title, image_url, link_url) VALUES 
    ('Amazing Electronics Deals', 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=400&fit=crop', 'https://amazon.in'),
    ('Fashion Sale', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=1200&h=400&fit=crop', 'https://amazon.in'),
    ('Home & Kitchen Essentials', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=1200&h=400&fit=crop', 'https://amazon.in')
ON CONFLICT DO NOTHING;

-- Insert sample email subscribers
INSERT INTO email_subscribers (email, name, subscription_source) VALUES 
    ('<EMAIL>', 'John Doe', 'manual'),
    ('<EMAIL>', 'Jane Smith', 'csv_import'),
    ('<EMAIL>', 'Bob Johnson', 'manual')
ON CONFLICT (email) DO NOTHING;

-- Insert default email templates
INSERT INTO email_templates (name, subject, html_content, template_type, is_default) VALUES 
(
    'Product Promotion',
    'Amazing Deals - Special Offers Just for You!',
    '<!DOCTYPE html><html><head><meta charset="utf-8"><title>Amazing Deals</title><style>body{font-family:Arial,sans-serif;margin:0;padding:0;background-color:#f5f5f5}.container{max-width:600px;margin:0 auto;background-color:white}.header{background:linear-gradient(135deg,#f97316,#ea580c);padding:20px;text-align:center}.header h1{color:white;margin:0;font-size:28px}.content{padding:30px}.button{background:linear-gradient(135deg,#f97316,#ea580c);color:white;padding:12px 24px;text-decoration:none;border-radius:6px;display:inline-block}.footer{background:#f9fafb;padding:20px;text-align:center;color:#6b7280;font-size:14px}</style></head><body><div class="container"><div class="header"><h1>🛍️ Amazing Deals</h1></div><div class="content"><h2>Special Offers Just for You!</h2><p>Hi {{subscriber_name}},</p><p>Check out our amazing deals!</p><p><a href="{{store_url}}" class="button">Shop Now</a></p></div><div class="footer"><p>© 2024 Amazing Deals. All rights reserved.</p></div></div></body></html>',
    'promotion',
    true
)
ON CONFLICT DO NOTHING;

-- Insert sample WhatsApp subscribers
INSERT INTO whatsapp_subscribers (phone_number, name, subscription_source) VALUES 
    ('+919876543210', 'Test User 1', 'manual'),
    ('+919876543211', 'Test User 2', 'manual')
ON CONFLICT (phone_number) DO NOTHING;

-- Insert default WhatsApp templates
INSERT INTO whatsapp_templates (name, template_name, body_text, footer_text, category) VALUES 
(
    'New Product Alert',
    'new_product_alert',
    'Hi {{1}}! 🛍️ Amazing new deal just arrived!\n\n*{{2}}*\n💰 Price: ₹{{3}}\n🎯 Discount: {{4}}% OFF',
    'Amazing Deals - Your trusted shopping companion',
    'MARKETING'
)
ON CONFLICT DO NOTHING;

-- Insert default email settings
INSERT INTO email_settings (smtp_host, smtp_port, from_email, from_name, is_active) VALUES 
('smtp.gmail.com', 587, '<EMAIL>', 'Amazing Deals', false)
ON CONFLICT DO NOTHING;

-- Insert default WhatsApp settings
INSERT INTO whatsapp_settings (business_account_id, is_active) VALUES 
('your_business_account_id', false)
ON CONFLICT DO NOTHING;
