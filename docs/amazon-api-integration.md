# Amazon Product Advertising API Integration Guide

## Overview

The Amazon Product Advertising API (PA API) is the official way to access Amazon product data for your affiliate website. This approach is:

- ✅ **Officially supported** by Amazon
- ✅ **Reliable** with high uptime
- ✅ **Comprehensive** with detailed product information
- ✅ **Legal** and compliant with Amazon's terms of service

## Prerequisites

1. **Amazon Associates Account**: You must be a member of the [Amazon Associates Program](https://affiliate-program.amazon.com/)
2. **PA API Access**: Register for the [Product Advertising API](https://webservices.amazon.com/paapi5/documentation/)
3. **API Credentials**: Obtain your Access Key ID and Secret Key

## Implementation Steps

### 1. Install the Official SDK

```bash
npm install amazon-paapi
```

### 2. Create API Configuration

Create a file at `src/lib/amazon-api.ts`:

```typescript
import { ProductAdvertisingAPIv1 } from 'amazon-paapi';

// Configure the client
const config = {
  accessKey: process.env.AMAZON_ACCESS_KEY || '',
  secretKey: process.env.AMAZON_SECRET_KEY || '',
  partnerTag: process.env.AMAZON_PARTNER_TAG || '', // Your Associate/Affiliate ID
  partnerType: 'Associates',
  marketplace: 'www.amazon.com', // Can be changed for different regions
  host: 'webservices.amazon.com',
  region: 'us-east-1' // Change based on your location
};

// Initialize the client
const api = new ProductAdvertisingAPIv1(config);

export default api;
```

### 3. Create Environment Variables

Add to your `.env` file:

```
AMAZON_ACCESS_KEY=your_access_key
AMAZON_SECRET_KEY=your_secret_key
AMAZON_PARTNER_TAG=your_associate_id
```

### 4. Create API Endpoint

Create a file at `src/app/api/amazon-product/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import api from '@/lib/amazon-api';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const asin = searchParams.get('asin');
  
  if (!asin) {
    return NextResponse.json(
      { error: 'ASIN parameter is required' },
      { status: 400 }
    );
  }
  
  try {
    const response = await api.getItems({
      ItemIds: [asin],
      Resources: [
        'Images.Primary.Large',
        'ItemInfo.Title',
        'ItemInfo.Features',
        'ItemInfo.ProductInfo',
        'ItemInfo.ByLineInfo',
        'Offers.Listings.Price'
      ]
    });
    
    if (response.ItemsResult?.Items && response.ItemsResult.Items.length > 0) {
      const item = response.ItemsResult.Items[0];
      
      return NextResponse.json({
        success: true,
        data: {
          title: item.ItemInfo?.Title?.DisplayValue,
          price: item.Offers?.Listings?.[0]?.Price?.DisplayAmount,
          image: item.Images?.Primary?.Large?.URL,
          features: item.ItemInfo?.Features?.DisplayValues,
          brand: item.ItemInfo?.ByLineInfo?.Brand?.DisplayValue,
          asin: item.ASIN,
          detailPageURL: item.DetailPageURL
        }
      });
    } else {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Amazon API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product data from Amazon' },
      { status: 500 }
    );
  }
}
```

### 5. Usage in Components

```typescript
// Example usage in a component
const fetchAmazonProduct = async (asin: string) => {
  const response = await fetch(`/api/amazon-product?asin=${asin}`);
  if (response.ok) {
    const data = await response.json();
    // Use the product data
    console.log(data);
  }
};
```

## Rate Limits and Quotas

- PA API has rate limits (typically 1 request per second)
- There are monthly quotas based on your Amazon Associates account
- Monitor your usage to avoid exceeding limits

## Best Practices

1. **Cache Results**: Store API responses to reduce API calls
2. **Error Handling**: Implement robust error handling
3. **Fallback Mechanism**: Have a manual entry option as backup
4. **Respect Rate Limits**: Implement throttling to avoid exceeding limits
5. **Keep Credentials Secure**: Never expose your API keys in client-side code

## Legal Considerations

- You must comply with [Amazon Associates Program Operating Agreement](https://affiliate-program.amazon.com/help/operating/agreement)
- Product data should only be used for promoting Amazon products
- Always include the required affiliate disclosure

## Resources

- [PA API Documentation](https://webservices.amazon.com/paapi5/documentation/)
- [Amazon Associates Help](https://affiliate-program.amazon.com/help/node/topic/GRXPHT8U84RAYDXZ)
- [SDK GitHub Repository](https://github.com/dmtrs/amazon-paapi)
