{"name": "reactaff", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "@types/bcryptjs": "^3.0.0", "@types/cheerio": "^1.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.5", "bcryptjs": "^3.0.2", "cheerio": "^1.1.2", "jsonwebtoken": "^9.0.2", "next": "15.4.5", "nodemailer": "^7.0.5", "pg": "^8.16.3", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}