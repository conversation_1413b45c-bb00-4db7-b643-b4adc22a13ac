// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id           String    @id @default(cuid())
  name         String
  email        String    @unique
  passwordHash String    @map("password_hash")
  role         UserRole  @default(USER)
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  products     Product[]

  @@map("users")
}

// Category model for product organization
model Category {
  id        String    @id @default(cuid())
  name      String    @unique
  slug      String    @unique
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")

  // Relations
  products  Product[]

  @@map("categories")
}

// Product model for affiliate products
model Product {
  id                  String   @id @default(cuid())
  name                String
  description         String?
  amazonAffiliateLink String   @map("amazon_affiliate_link")
  imageUrl            String?  @map("image_url")
  price               Decimal? @db.Decimal(10, 2)
  categoryId          String   @map("category_id")
  createdById         String   @map("created_by")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  category            Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  createdBy           User     @relation(fields: [createdById], references: [id], onDelete: Cascade)

  @@map("products")
}

// Enum for user roles
enum UserRole {
  ADMIN
  USER
}
