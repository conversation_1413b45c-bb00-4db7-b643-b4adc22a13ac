import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Admin User',
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: 'ADMIN',
    },
  })

  console.log('👤 Created admin user:', adminUser.email)

  // Create categories
  const categories = [
    { name: 'Electronics', slug: 'electronics' },
    { name: 'Books', slug: 'books' },
    { name: 'Home & Garden', slug: 'home-garden' },
    { name: 'Sports & Outdoors', slug: 'sports-outdoors' },
    { name: 'Health & Beauty', slug: 'health-beauty' },
  ]

  const createdCategories = []
  for (const category of categories) {
    const createdCategory = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdCategories.push(createdCategory)
    console.log('📂 Created category:', createdCategory.name)
  }

  // Create sample products
  const products = [
    {
      name: 'iPhone 15 Pro',
      description: 'Latest iPhone with advanced camera system and A17 Pro chip',
      amazonAffiliateLink: 'https://amazon.com/dp/B0CHX1W1XY?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',
      price: 999.99,
      categorySlug: 'electronics',
    },
    {
      name: 'MacBook Air M3',
      description: 'Powerful laptop with M3 chip and all-day battery life',
      amazonAffiliateLink: 'https://amazon.com/dp/B0CX23V2ZK?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-**********-5c52b6b3adef?w=400',
      price: 1299.99,
      categorySlug: 'electronics',
    },
    {
      name: 'The Psychology of Money',
      description: 'Timeless lessons on wealth, greed, and happiness',
      amazonAffiliateLink: 'https://amazon.com/dp/0857197681?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400',
      price: 14.99,
      categorySlug: 'books',
    },
    {
      name: 'Atomic Habits',
      description: 'An easy & proven way to build good habits & break bad ones',
      amazonAffiliateLink: 'https://amazon.com/dp/0735211299?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
      price: 13.99,
      categorySlug: 'books',
    },
    {
      name: 'Robot Vacuum Cleaner',
      description: 'Smart robot vacuum with mapping and app control',
      amazonAffiliateLink: 'https://amazon.com/dp/B08XYQZQZX?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
      price: 299.99,
      categorySlug: 'home-garden',
    },
    {
      name: 'Yoga Mat Premium',
      description: 'Non-slip exercise mat for yoga, pilates, and fitness',
      amazonAffiliateLink: 'https://amazon.com/dp/B01LXQZ8ZX?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400',
      price: 39.99,
      categorySlug: 'sports-outdoors',
    },
    {
      name: 'Skincare Set',
      description: 'Complete skincare routine with cleanser, serum, and moisturizer',
      amazonAffiliateLink: 'https://amazon.com/dp/B08XYZABC1?tag=youraffid-20',
      imageUrl: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=400',
      price: 79.99,
      categorySlug: 'health-beauty',
    },
  ]

  for (const product of products) {
    const category = createdCategories.find(c => c.slug === product.categorySlug)
    if (category) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: { name: product.name }
      })

      if (!existingProduct) {
        const createdProduct = await prisma.product.create({
          data: {
            name: product.name,
            description: product.description,
            amazonAffiliateLink: product.amazonAffiliateLink,
            imageUrl: product.imageUrl,
            price: product.price,
            categoryId: category.id,
            createdById: adminUser.id,
          },
        })
        console.log('🛍️ Created product:', createdProduct.name)
      } else {
        console.log('🛍️ Product already exists:', product.name)
      }
    }
  }

  console.log('✅ Database seeded successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
