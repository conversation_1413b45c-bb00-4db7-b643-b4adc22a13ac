import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkDatabase() {
  try {
    console.log('🔍 Checking database connection...')
    
    // Test connection
    await prisma.$connect()
    console.log('✅ Database connection successful!')
    
    // Check if tables exist and have data
    const userCount = await prisma.user.count()
    const categoryCount = await prisma.category.count()
    const productCount = await prisma.product.count()
    
    console.log(`📊 Database Statistics:`)
    console.log(`   Users: ${userCount}`)
    console.log(`   Categories: ${categoryCount}`)
    console.log(`   Products: ${productCount}`)
    
    if (userCount === 0 || categoryCount === 0 || productCount === 0) {
      console.log('⚠️  Database appears to be empty or missing data')
      console.log('🌱 Running seed script...')
      
      // Import and run seed
      const { execSync } = require('child_process')
      execSync('npm run db:seed', { stdio: 'inherit' })
      
      console.log('✅ Seed completed!')
    } else {
      console.log('✅ Database has data!')
      
      // Show sample data
      const sampleUser = await prisma.user.findFirst()
      const sampleCategory = await prisma.category.findFirst()
      const sampleProduct = await prisma.product.findFirst({
        include: { category: true }
      })
      
      console.log('\n📋 Sample Data:')
      console.log(`   Admin User: ${sampleUser?.email}`)
      console.log(`   Category: ${sampleCategory?.name}`)
      console.log(`   Product: ${sampleProduct?.name} (${sampleProduct?.category.name})`)
    }
    
  } catch (error) {
    console.error('❌ Database error:', error)
    
    if (error instanceof Error && error.message.includes('connect')) {
      console.log('\n🔧 Database connection failed. Checking environment...')
      console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set')
      
      if (!process.env.DATABASE_URL) {
        console.log('⚠️  DATABASE_URL not found in environment variables')
        console.log('Please check your .env file')
      }
    }
  } finally {
    await prisma.$disconnect()
  }
}

checkDatabase()
