import bcrypt from 'bcryptjs'

async function generateNewPasswordHash() {
  try {
    console.log('🔐 Generating new password hash for admin user...')
    
    const password = 'admin123'
    const hash = await bcrypt.hash(password, 12)
    
    console.log('Password:', password)
    console.log('New hash:', hash)
    
    // Test the hash
    const isValid = await bcrypt.compare(password, hash)
    console.log('Hash verification test:', isValid)
    
    console.log('\n📋 SQL command to update the password:')
    console.log(`UPDATE users SET password_hash = '${hash}' WHERE email = '<EMAIL>';`)
    
  } catch (error) {
    console.error('❌ Failed to generate password hash:', error)
  }
}

generateNewPasswordHash()
