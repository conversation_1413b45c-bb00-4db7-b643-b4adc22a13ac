-- Create affiliate store database schema
-- Drop existing tables if they exist
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Create users table
CREATE TABLE users (
    id VARCHAR(30) PRIMARY KEY DEFAULT 'user_' || EXTRACT(EPOCH FROM NOW())::BIGINT || '_' || FLOOR(RANDOM() * 1000000)::TEXT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'USER' CHECK (role IN ('ADMIN', 'USER')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE categories (
    id VARCHAR(30) PRIMARY KEY DEFAULT 'cat_' || EXTRACT(EPOCH FROM NOW())::BIGINT || '_' || FLOOR(RANDOM() * 1000000)::TEXT,
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products table
CREATE TABLE products (
    id VARCHAR(30) PRIMARY KEY DEFAULT 'prod_' || EXTRACT(EPOCH FROM NOW())::BIGINT || '_' || FLOOR(RANDOM() * 1000000)::TEXT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    amazon_affiliate_link TEXT NOT NULL,
    image_url TEXT,
    price DECIMAL(10, 2),
    category_id VARCHAR(30) NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    created_by VARCHAR(30) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_created_by ON products(created_by);
CREATE INDEX idx_products_created_at ON products(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert admin user (password: admin123)
INSERT INTO users (id, name, email, password_hash, role) VALUES 
('admin_user_001', 'Admin User', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.LG7my', 'ADMIN');

-- Insert categories
INSERT INTO categories (id, name, slug) VALUES
('cat_electronics', 'Electronics', 'electronics'),
('cat_books', 'Books', 'books'),
('cat_home_garden', 'Home & Garden', 'home-garden'),
('cat_sports', 'Sports & Outdoors', 'sports-outdoors'),
('cat_health_beauty', 'Health & Beauty', 'health-beauty');

-- Insert sample products
INSERT INTO products (id, name, description, amazon_affiliate_link, image_url, price, category_id, created_by) VALUES
('prod_iphone15', 'iPhone 15 Pro', 'Latest iPhone with advanced camera system and A17 Pro chip', 'https://amazon.com/dp/B0CHX1W1XY?tag=youraffid-20', 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400', 999.99, 'cat_electronics', 'admin_user_001'),
('prod_macbook', 'MacBook Air M3', 'Powerful laptop with M3 chip and all-day battery life', 'https://amazon.com/dp/B0CX23V2ZK?tag=youraffid-20', 'https://images.unsplash.com/photo-**********-5c52b6b3adef?w=400', 1299.99, 'cat_electronics', 'admin_user_001'),
('prod_psychology_money', 'The Psychology of Money', 'Timeless lessons on wealth, greed, and happiness', 'https://amazon.com/dp/**********?tag=youraffid-20', 'https://images.unsplash.com/photo-**********-fa07a98d237f?w=400', 14.99, 'cat_books', 'admin_user_001'),
('prod_atomic_habits', 'Atomic Habits', 'An easy & proven way to build good habits & break bad ones', 'https://amazon.com/dp/0735211299?tag=youraffid-20', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400', 13.99, 'cat_books', 'admin_user_001'),
('prod_robot_vacuum', 'Robot Vacuum Cleaner', 'Smart robot vacuum with mapping and app control', 'https://amazon.com/dp/B08XYQZQZX?tag=youraffid-20', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400', 299.99, 'cat_home_garden', 'admin_user_001'),
('prod_yoga_mat', 'Yoga Mat Premium', 'Non-slip exercise mat for yoga, pilates, and fitness', 'https://amazon.com/dp/B01LXQZ8ZX?tag=youraffid-20', 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400', 39.99, 'cat_sports', 'admin_user_001'),
('prod_skincare_set', 'Skincare Set', 'Complete skincare routine with cleanser, serum, and moisturizer', 'https://amazon.com/dp/B08XYZABC1?tag=youraffid-20', 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=400', 79.99, 'cat_health_beauty', 'admin_user_001');
