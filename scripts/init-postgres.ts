import { initializeDatabase } from '../src/lib/postgres'
import { createUser, createCategory, createProduct } from '../src/lib/database'
import bcrypt from 'bcryptjs'

async function initializeAndSeed() {
  try {
    console.log('🚀 Initializing PostgreSQL database...')
    
    // Initialize database tables
    await initializeDatabase()
    
    console.log('🌱 Seeding database with initial data...')
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    const adminUser = await createUser({
      name: 'Admin User',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      role: 'ADMIN'
    })
    console.log('👤 Created admin user:', adminUser.email)
    
    // Create categories
    const categories = [
      { name: 'Electronics', slug: 'electronics' },
      { name: 'Books', slug: 'books' },
      { name: 'Home & Garden', slug: 'home-garden' },
      { name: 'Sports & Outdoors', slug: 'sports-outdoors' },
      { name: 'Health & Beauty', slug: 'health-beauty' }
    ]
    
    const createdCategories = []
    for (const categoryData of categories) {
      const category = await createCategory(categoryData)
      createdCategories.push(category)
      console.log('📂 Created category:', category.name)
    }
    
    // Create products
    const products = [
      {
        name: 'iPhone 15 Pro',
        description: 'Latest iPhone with advanced camera system and A17 Pro chip',
        amazon_affiliate_link: 'https://amazon.com/dp/B0CHX1W1XY?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',
        price: 999.99,
        category_id: createdCategories[0].id, // Electronics
        created_by: adminUser.id
      },
      {
        name: 'MacBook Air M3',
        description: 'Powerful laptop with M3 chip and all-day battery life',
        amazon_affiliate_link: 'https://amazon.com/dp/B0CX23V2ZK?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400',
        price: 1299.99,
        category_id: createdCategories[0].id, // Electronics
        created_by: adminUser.id
      },
      {
        name: 'The Psychology of Money',
        description: 'Timeless lessons on wealth, greed, and happiness',
        amazon_affiliate_link: 'https://amazon.com/dp/0857197681?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400',
        price: 14.99,
        category_id: createdCategories[1].id, // Books
        created_by: adminUser.id
      },
      {
        name: 'Atomic Habits',
        description: 'An easy & proven way to build good habits & break bad ones',
        amazon_affiliate_link: 'https://amazon.com/dp/0735211299?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
        price: 13.99,
        category_id: createdCategories[1].id, // Books
        created_by: adminUser.id
      },
      {
        name: 'Robot Vacuum Cleaner',
        description: 'Smart robot vacuum with mapping and app control',
        amazon_affiliate_link: 'https://amazon.com/dp/B08XYQZQZX?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
        price: 299.99,
        category_id: createdCategories[2].id, // Home & Garden
        created_by: adminUser.id
      },
      {
        name: 'Yoga Mat Premium',
        description: 'Non-slip exercise mat for yoga, pilates, and fitness',
        amazon_affiliate_link: 'https://amazon.com/dp/B01LXQZ8ZX?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
        price: 39.99,
        category_id: createdCategories[3].id, // Sports & Outdoors
        created_by: adminUser.id
      },
      {
        name: 'Skincare Set',
        description: 'Complete skincare routine with cleanser, serum, and moisturizer',
        amazon_affiliate_link: 'https://amazon.com/dp/B08XYZABC1?tag=youraffid-20',
        image_url: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=400',
        price: 79.99,
        category_id: createdCategories[4].id, // Health & Beauty
        created_by: adminUser.id
      }
    ]
    
    for (const productData of products) {
      const product = await createProduct(productData)
      console.log('🛍️ Created product:', product.name)
    }
    
    console.log('✅ Database initialization and seeding completed successfully!')
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    process.exit(1)
  }
}

initializeAndSeed()
