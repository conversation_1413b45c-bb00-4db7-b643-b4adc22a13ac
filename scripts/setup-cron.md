# Automated Price Update Setup Guide

## Overview
This guide explains how to set up automated price updates for the Amazing Deals affiliate store.

## Prerequisites

### Linux/macOS/WSL
- `curl` command-line tool
- `jq` JSON processor
- Bash shell
- Cron daemon

### Windows
- PowerShell or WSL (Windows Subsystem for Linux)
- curl (available in Windows 10+)

## Installation

### 1. Install Dependencies

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install curl jq
```

#### macOS:
```bash
brew install curl jq
```

#### Windows (PowerShell as Administrator):
```powershell
# curl is built-in to Windows 10+
# For jq, download from: https://stedolan.github.io/jq/download/
```

### 2. Make Script Executable (Linux/macOS/WSL)
```bash
chmod +x scripts/update-prices.sh
```

## Manual Usage

### Run Price Update Once
```bash
# Linux/macOS/WSL
./scripts/update-prices.sh

# Windows
scripts\update-prices.bat
```

### Dry Run (See what would be updated)
```bash
./scripts/update-prices.sh --dry-run
```

### Help
```bash
./scripts/update-prices.sh --help
```

## Automated Scheduling

### Using Cron (Linux/macOS/WSL)

1. **Open crontab editor:**
```bash
crontab -e
```

2. **Add one of these schedules:**

#### Every 6 hours:
```bash
0 */6 * * * /path/to/your/project/scripts/update-prices.sh >> /path/to/your/project/logs/cron.log 2>&1
```

#### Daily at 2 AM:
```bash
0 2 * * * /path/to/your/project/scripts/update-prices.sh >> /path/to/your/project/logs/cron.log 2>&1
```

#### Twice daily (6 AM and 6 PM):
```bash
0 6,18 * * * /path/to/your/project/scripts/update-prices.sh >> /path/to/your/project/logs/cron.log 2>&1
```

#### Weekly on Sundays at 3 AM:
```bash
0 3 * * 0 /path/to/your/project/scripts/update-prices.sh >> /path/to/your/project/logs/cron.log 2>&1
```

3. **Save and exit** (Ctrl+X, then Y, then Enter in nano)

4. **Verify cron job:**
```bash
crontab -l
```

### Using Windows Task Scheduler

1. **Open Task Scheduler** (taskschd.msc)

2. **Create Basic Task:**
   - Name: "Amazing Deals Price Update"
   - Description: "Automated price updates for affiliate products"

3. **Set Trigger:**
   - Daily, Weekly, or Custom schedule
   - Choose appropriate time (e.g., 2:00 AM)

4. **Set Action:**
   - Program: `cmd.exe`
   - Arguments: `/c "C:\path\to\your\project\scripts\update-prices.bat"`
   - Start in: `C:\path\to\your\project`

5. **Configure Settings:**
   - Run whether user is logged on or not
   - Run with highest privileges

### Using systemd Timer (Linux)

1. **Create service file:**
```bash
sudo nano /etc/systemd/system/amazing-deals-price-update.service
```

```ini
[Unit]
Description=Amazing Deals Price Update
After=network.target

[Service]
Type=oneshot
User=your-username
WorkingDirectory=/path/to/your/project
ExecStart=/path/to/your/project/scripts/update-prices.sh
StandardOutput=append:/path/to/your/project/logs/systemd.log
StandardError=append:/path/to/your/project/logs/systemd-error.log
```

2. **Create timer file:**
```bash
sudo nano /etc/systemd/system/amazing-deals-price-update.timer
```

```ini
[Unit]
Description=Run Amazing Deals Price Update
Requires=amazing-deals-price-update.service

[Timer]
OnCalendar=*-*-* 02:00:00
Persistent=true

[Install]
WantedBy=timers.target
```

3. **Enable and start timer:**
```bash
sudo systemctl daemon-reload
sudo systemctl enable amazing-deals-price-update.timer
sudo systemctl start amazing-deals-price-update.timer
```

4. **Check timer status:**
```bash
sudo systemctl status amazing-deals-price-update.timer
sudo systemctl list-timers amazing-deals-price-update.timer
```

## Configuration

### Environment Variables
You can customize the script behavior by setting these environment variables:

```bash
export API_BASE_URL="http://localhost:3000"
export RATE_LIMIT_DELAY=2
export MAX_RETRIES=3
export BATCH_SIZE=10
```

### Admin Credentials
Update the admin credentials in the script:
```bash
# In update-prices.sh, modify this line:
-d '{"email":"<EMAIL>","password":"admin123"}'
```

## Monitoring and Logs

### Log Files
- **Main log:** `logs/price-update-YYYYMMDD-HHMMSS.log`
- **Error log:** `logs/price-update-errors-YYYYMMDD-HHMMSS.log`
- **Cron log:** `logs/cron.log` (if configured)

### Check Recent Logs
```bash
# View latest price update log
ls -la logs/price-update-*.log | tail -1 | xargs cat

# View recent errors
ls -la logs/price-update-errors-*.log | tail -1 | xargs cat

# Monitor live updates
tail -f logs/price-update-*.log
```

### Log Rotation
Add to crontab to clean old logs (keep last 30 days):
```bash
0 1 * * * find /path/to/your/project/logs -name "price-update-*.log" -mtime +30 -delete
```

## Troubleshooting

### Common Issues

1. **Permission Denied:**
```bash
chmod +x scripts/update-prices.sh
```

2. **Command Not Found (jq):**
```bash
# Install jq
sudo apt install jq  # Ubuntu/Debian
brew install jq      # macOS
```

3. **API Connection Failed:**
- Check if the application is running on localhost:3000
- Verify admin credentials
- Check firewall settings

4. **Rate Limiting:**
- Increase `RATE_LIMIT_DELAY` in the script
- Reduce `BATCH_SIZE` for slower processing

### Testing
```bash
# Test authentication
curl -X POST "http://localhost:3000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Test product fetch
curl -X GET "http://localhost:3000/api/products?limit=5"

# Test Amazon scraper
curl -X POST "http://localhost:3000/api/scrape-amazon" \
  -H "Content-Type: application/json" \
  -d '{"amazonUrl":"https://amazon.com/dp/B08N5WRWNW"}'
```

## Best Practices

1. **Schedule during low-traffic hours** (e.g., 2-4 AM)
2. **Monitor logs regularly** for errors
3. **Set up alerts** for failed updates
4. **Backup database** before major updates
5. **Test script manually** before scheduling
6. **Use rate limiting** to avoid being blocked by Amazon
7. **Keep logs for debugging** but rotate old ones

## Security Notes

- Store admin credentials securely
- Use environment variables for sensitive data
- Restrict script permissions
- Monitor for unauthorized access
- Consider using API keys instead of passwords
