// Comprehensive functionality test script
async function testAllFunctionality() {
  console.log('🧪 Comprehensive Functionality Test\n')

  const baseUrl = 'http://localhost:3000'
  const results = {
    passed: 0,
    failed: 0,
    issues: [] as string[]
  }

  const test = async (name: string, testFn: () => Promise<boolean>) => {
    try {
      const result = await testFn()
      if (result) {
        console.log(`✅ ${name}`)
        results.passed++
      } else {
        console.log(`❌ ${name}`)
        results.failed++
        results.issues.push(name)
      }
    } catch (error) {
      console.log(`❌ ${name} - Error: ${error}`)
      results.failed++
      results.issues.push(`${name} (Error: ${error})`)
    }
  }

  // Test 1: API Endpoints
  console.log('📡 Testing API Endpoints...')
  
  await test('Categories API', async () => {
    const response = await fetch(`${baseUrl}/api/categories`)
    return response.ok
  })

  await test('Products API', async () => {
    const response = await fetch(`${baseUrl}/api/products`)
    return response.ok
  })

  await test('Search API', async () => {
    const response = await fetch(`${baseUrl}/api/search?q=iphone`)
    return response.ok
  })

  await test('Single Product API', async () => {
    const response = await fetch(`${baseUrl}/api/products/prod_iphone15`)
    return response.ok
  })

  // Test 2: Authentication
  console.log('\n🔐 Testing Authentication...')
  
  let authToken = ''
  await test('Admin Login', async () => {
    const response = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      authToken = data.token
      return true
    }
    return false
  })

  await test('Protected Route Access', async () => {
    if (!authToken) return false
    
    const response = await fetch(`${baseUrl}/api/auth/me`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    })
    return response.ok
  })

  // Test 3: CRUD Operations
  console.log('\n📝 Testing CRUD Operations...')
  
  let testCategoryId = ''
  await test('Create Category', async () => {
    if (!authToken) return false
    
    const response = await fetch(`${baseUrl}/api/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: 'Test Category ' + Date.now(),
        slug: 'test-category-' + Date.now()
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      testCategoryId = data.category.id
      return true
    }
    return false
  })

  let testProductId = ''
  await test('Create Product', async () => {
    if (!authToken || !testCategoryId) return false
    
    const response = await fetch(`${baseUrl}/api/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: 'Test Product ' + Date.now(),
        description: 'Test product description',
        amazonAffiliateLink: 'https://amazon.com/dp/TEST123',
        imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',
        price: 99.99,
        categoryId: testCategoryId
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      testProductId = data.product.id
      return true
    }
    return false
  })

  await test('Update Product', async () => {
    if (!authToken || !testProductId) return false
    
    const response = await fetch(`${baseUrl}/api/products/${testProductId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: 'Updated Test Product',
        description: 'Updated description',
        amazonAffiliateLink: 'https://amazon.com/dp/UPDATED123',
        price: 149.99,
        categoryId: testCategoryId
      })
    })
    
    return response.ok
  })

  await test('Delete Product', async () => {
    if (!authToken || !testProductId) return false
    
    const response = await fetch(`${baseUrl}/api/products/${testProductId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    })
    
    return response.ok
  })

  await test('Delete Category', async () => {
    if (!authToken || !testCategoryId) return false
    
    const response = await fetch(`${baseUrl}/api/categories/${testCategoryId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    })
    
    return response.ok
  })

  // Test 4: Page Routes
  console.log('\n🌐 Testing Page Routes...')
  
  const testRoute = async (route: string) => {
    const response = await fetch(`${baseUrl}${route}`)
    return response.status === 200
  }

  await test('Homepage', () => testRoute('/'))
  await test('Admin Login Page', () => testRoute('/admin/login'))
  await test('Admin Dashboard', () => testRoute('/admin'))
  await test('Admin Products Page', () => testRoute('/admin/products'))
  await test('Admin Categories Page', () => testRoute('/admin/categories'))
  await test('New Product Page', () => testRoute('/admin/products/new'))
  await test('New Category Page', () => testRoute('/admin/categories/new'))
  await test('Edit Product Page', () => testRoute('/admin/products/prod_iphone15/edit'))

  // Test 5: Data Integrity
  console.log('\n🔍 Testing Data Integrity...')
  
  await test('Products have required fields', async () => {
    const response = await fetch(`${baseUrl}/api/products?limit=1`)
    if (!response.ok) return false
    
    const data = await response.json()
    const product = data.products[0]
    
    return !!(product.id && product.name && product.amazonAffiliateLink && product.category)
  })

  await test('Categories have product counts', async () => {
    const response = await fetch(`${baseUrl}/api/categories`)
    if (!response.ok) return false
    
    const data = await response.json()
    const category = data.categories[0]
    
    return !!(category._count && typeof category._count.products === 'number')
  })

  await test('Image URLs are accessible', async () => {
    const response = await fetch(`${baseUrl}/api/products?limit=1`)
    if (!response.ok) return false
    
    const data = await response.json()
    const product = data.products[0]
    
    if (!product.imageUrl) return true // No image is acceptable
    
    const imageResponse = await fetch(product.imageUrl, { method: 'HEAD' })
    return imageResponse.ok
  })

  // Summary
  console.log('\n📊 Test Summary:')
  console.log(`✅ Passed: ${results.passed}`)
  console.log(`❌ Failed: ${results.failed}`)
  console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`)
  
  if (results.issues.length > 0) {
    console.log('\n🚨 Issues Found:')
    results.issues.forEach(issue => console.log(`   - ${issue}`))
  } else {
    console.log('\n🎉 All tests passed!')
  }
}

testAllFunctionality()
