import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testEverything() {
  console.log('🧪 Running comprehensive tests...\n')

  // Test 1: Database Connection
  console.log('1️⃣ Testing Database Connection...')
  try {
    await prisma.$connect()
    const userCount = await prisma.user.count()
    const categoryCount = await prisma.category.count()
    const productCount = await prisma.product.count()
    
    console.log(`✅ Database connected successfully`)
    console.log(`   - Users: ${userCount}`)
    console.log(`   - Categories: ${categoryCount}`)
    console.log(`   - Products: ${productCount}`)
    
    if (userCount === 0 || categoryCount === 0 || productCount === 0) {
      console.log('⚠️  Missing data detected')
      return false
    }
  } catch (error) {
    console.log('❌ Database connection failed:', error)
    return false
  }

  // Test 2: API Endpoints
  console.log('\n2️⃣ Testing API Endpoints...')
  const baseUrl = 'http://localhost:3000'
  
  try {
    // Test public endpoints
    const endpoints = [
      '/api/categories',
      '/api/products',
      '/api/products?limit=5',
      '/api/search?q=phone'
    ]
    
    for (const endpoint of endpoints) {
      const response = await fetch(`${baseUrl}${endpoint}`)
      if (response.ok) {
        console.log(`✅ ${endpoint} - Status: ${response.status}`)
      } else {
        console.log(`❌ ${endpoint} - Status: ${response.status}`)
        return false
      }
    }
  } catch (error) {
    console.log('❌ API test failed:', error)
    return false
  }

  // Test 3: Authentication
  console.log('\n3️⃣ Testing Authentication...')
  try {
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json()
      console.log(`✅ Admin login successful - User: ${loginData.user.email}`)
      
      // Test protected endpoint
      const token = loginData.token
      const protectedResponse = await fetch(`${baseUrl}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (protectedResponse.ok) {
        console.log(`✅ Protected endpoint accessible with token`)
      } else {
        console.log(`❌ Protected endpoint failed - Status: ${protectedResponse.status}`)
        return false
      }
    } else {
      console.log(`❌ Admin login failed - Status: ${loginResponse.status}`)
      return false
    }
  } catch (error) {
    console.log('❌ Authentication test failed:', error)
    return false
  }

  // Test 4: Data Integrity
  console.log('\n4️⃣ Testing Data Integrity...')
  try {
    const products = await prisma.product.findMany({
      include: {
        category: true,
        createdBy: true
      }
    })
    
    let validProducts = 0
    for (const product of products) {
      if (product.name && product.amazonAffiliateLink && product.category && product.createdBy) {
        validProducts++
      }
    }
    
    console.log(`✅ Data integrity check: ${validProducts}/${products.length} products valid`)
    
    if (validProducts !== products.length) {
      console.log('⚠️  Some products have missing required data')
    }
  } catch (error) {
    console.log('❌ Data integrity test failed:', error)
    return false
  }

  console.log('\n🎉 All tests passed successfully!')
  return true
}

async function main() {
  try {
    const success = await testEverything()
    process.exit(success ? 0 : 1)
  } catch (error) {
    console.error('❌ Test suite failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
