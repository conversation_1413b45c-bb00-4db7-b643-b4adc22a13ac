import bcrypt from 'bcryptjs'
import { findUserByEmail } from '../src/lib/database'

async function testAuthentication() {
  try {
    console.log('🔐 Testing authentication flow...')
    
    // Test 1: Check if user exists
    console.log('\n1️⃣ Testing user lookup...')
    const user = await findUserByEmail('<EMAIL>')
    
    if (!user) {
      console.log('❌ User not found in database')
      return
    }
    
    console.log('✅ User found:', {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
    })
    
    // Test 2: Check password hash
    console.log('\n2️⃣ Testing password verification...')
    const testPassword = 'admin123'
    const storedHash = user.password_hash
    
    console.log('Password to test:', testPassword)
    console.log('Stored hash:', storedHash)
    
    // Test with bcrypt
    const isValid = await bcrypt.compare(testPassword, storedHash)
    console.log('Password verification result:', isValid)
    
    if (isValid) {
      console.log('✅ Password verification successful')
    } else {
      console.log('❌ Password verification failed')
      
      // Test if the hash was created correctly
      console.log('\n🔍 Testing hash creation...')
      const newHash = await bcrypt.hash(testPassword, 12)
      console.log('New hash for same password:', newHash)
      
      const testNewHash = await bcrypt.compare(testPassword, newHash)
      console.log('New hash verification:', testNewHash)
    }
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error)
  }
}

testAuthentication()
