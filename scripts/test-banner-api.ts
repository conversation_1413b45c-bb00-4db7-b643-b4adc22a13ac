// Test script for banner API functionality
async function testBannerAPI() {
  console.log('🚀 Testing Banner API\n')

  const baseUrl = 'http://localhost:3000'
  
  // Test payload from the user
  const testPayload = {
    "title": "ABC",
    "displayOrder": 1,
    "endDate": "2025-08-10T16:14",
    "imageUrl": "https://m.media-amazon.com/images/G/31/img2020/fashion/WA_2020/Augart25/WASCTS/WAlivnow_1074x294.png",
    "isActive": true,
    "linkUrl": "https://www.amazon.in/",
    "startDate": "2025-07-31T16:14"
  }

  console.log('Test payload:', JSON.stringify(testPayload, null, 2))

  // Test 1: Check banner API without auth (should work for GET)
  console.log('\n1️⃣ Testing GET /api/banners...')
  
  try {
    const response = await fetch(`${baseUrl}/api/banners`)
    console.log(`Status: ${response.status}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log(`✅ GET banners successful - found ${data.banners?.length || 0} banners`)
    } else {
      const error = await response.text()
      console.log(`❌ GET banners failed: ${error}`)
    }
  } catch (error) {
    console.log(`❌ GET banners error: ${error}`)
  }

  // Test 2: Test POST without authentication (should fail)
  console.log('\n2️⃣ Testing POST /api/banners without auth...')
  
  try {
    const response = await fetch(`${baseUrl}/api/banners`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    })
    
    console.log(`Status: ${response.status}`)
    const responseText = await response.text()
    console.log(`Response: ${responseText}`)
    
    if (response.status === 401) {
      console.log('✅ Correctly requires authentication')
    } else {
      console.log('⚠️ Unexpected response for unauthenticated request')
    }
  } catch (error) {
    console.log(`❌ POST banners error: ${error}`)
  }

  // Test 3: Try to get admin token
  console.log('\n3️⃣ Testing authentication...')
  
  try {
    const authResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    })
    
    console.log(`Auth status: ${authResponse.status}`)
    
    if (authResponse.ok) {
      const authData = await authResponse.json()
      const token = authData.token
      console.log('✅ Authentication successful')
      
      // Test 4: Test POST with authentication
      console.log('\n4️⃣ Testing POST /api/banners with auth...')
      
      try {
        const response = await fetch(`${baseUrl}/api/banners`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(testPayload),
        })
        
        console.log(`Status: ${response.status}`)
        const responseText = await response.text()
        console.log(`Response: ${responseText}`)
        
        if (response.ok) {
          console.log('✅ Banner creation successful!')
          const data = JSON.parse(responseText)
          console.log(`Created banner with ID: ${data.banner?.id}`)
        } else if (response.status === 400) {
          console.log('❌ Validation error - checking details...')
          try {
            const errorData = JSON.parse(responseText)
            if (errorData.details) {
              console.log('Validation errors:')
              errorData.details.forEach((detail: any) => {
                console.log(`  - ${detail.path?.join('.')}: ${detail.message}`)
              })
            }
          } catch (e) {
            console.log('Could not parse error details')
          }
        } else {
          console.log('❌ Banner creation failed')
        }
      } catch (error) {
        console.log(`❌ POST with auth error: ${error}`)
      }
      
    } else {
      const authError = await authResponse.text()
      console.log(`❌ Authentication failed: ${authError}`)
      console.log('⚠️ Cannot test authenticated banner creation')
    }
  } catch (error) {
    console.log(`❌ Authentication error: ${error}`)
  }

  // Test 5: Test validation schema directly
  console.log('\n5️⃣ Testing validation schema...')
  
  const validationTests = [
    {
      name: 'Valid payload',
      payload: testPayload,
      shouldPass: true
    },
    {
      name: 'Missing title',
      payload: { ...testPayload, title: '' },
      shouldPass: false
    },
    {
      name: 'Invalid image URL',
      payload: { ...testPayload, imageUrl: 'not-a-url' },
      shouldPass: false
    },
    {
      name: 'Empty link URL (should be allowed)',
      payload: { ...testPayload, linkUrl: '' },
      shouldPass: true
    },
    {
      name: 'Undefined link URL (should be allowed)',
      payload: { ...testPayload, linkUrl: undefined },
      shouldPass: true
    }
  ]

  for (const test of validationTests) {
    console.log(`\nTesting: ${test.name}`)
    console.log(`Payload: ${JSON.stringify(test.payload)}`)
    console.log(`Expected to ${test.shouldPass ? 'pass' : 'fail'}`)
  }

  console.log('\n📋 Summary:')
  console.log('- Test the banner API endpoints')
  console.log('- Check authentication requirements')
  console.log('- Validate the test payload')
  console.log('- Debug any validation errors')
  
  console.log('\n💡 To fix validation issues:')
  console.log('1. Check the banner schema in /api/banners/route.ts')
  console.log('2. Ensure field names match exactly')
  console.log('3. Verify datetime format requirements')
  console.log('4. Test with simplified payload first')
  
  console.log('\n🎉 Banner API test completed!')
}

testBannerAPI()
