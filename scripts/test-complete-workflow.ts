// Test script to verify the complete workflow
import { query } from '../src/lib/postgres'

async function testCompleteWorkflow() {
  try {
    console.log('🧪 Testing complete affiliate store workflow...\n')

    // Test 1: Database Connection
    console.log('1️⃣ Testing Database Connection...')
    const versionResult = await query('SELECT version()')
    console.log('✅ Database connected successfully')

    // Test 2: Verify Tables and Data
    console.log('\n2️⃣ Verifying Database Structure...')
    const userCount = await query('SELECT COUNT(*) as count FROM users')
    const categoryCount = await query('SELECT COUNT(*) as count FROM categories')
    const productCount = await query('SELECT COUNT(*) as count FROM products')
    
    console.log(`✅ Users: ${userCount.rows[0].count}`)
    console.log(`✅ Categories: ${categoryCount.rows[0].count}`)
    console.log(`✅ Products: ${productCount.rows[0].count}`)

    // Test 3: Test Category Creation
    console.log('\n3️⃣ Testing Category Creation...')
    const testCategoryName = 'Test Category ' + Date.now()
    const testCategorySlug = 'test-category-' + Date.now()
    
    const newCategory = await query(
      'INSERT INTO categories (name, slug) VALUES ($1, $2) RETURNING *',
      [testCategoryName, testCategorySlug]
    )
    console.log(`✅ Created test category: ${newCategory.rows[0].name}`)

    // Test 4: Test Product Creation with New Category
    console.log('\n4️⃣ Testing Product Creation...')
    const adminUser = await query('SELECT id FROM users WHERE role = $1 LIMIT 1', ['ADMIN'])
    
    if (adminUser.rows.length > 0) {
      const testProduct = await query(`
        INSERT INTO products (name, description, amazon_affiliate_link, price, category_id, created_by) 
        VALUES ($1, $2, $3, $4, $5, $6) RETURNING *
      `, [
        'Test Product ' + Date.now(),
        'This is a test product created by the workflow test',
        'https://amazon.com/dp/TEST123?tag=test-20',
        99.99,
        newCategory.rows[0].id,
        adminUser.rows[0].id
      ])
      console.log(`✅ Created test product: ${testProduct.rows[0].name}`)
    }

    // Test 5: Test Category with Product Count
    console.log('\n5️⃣ Testing Category with Product Count...')
    const categoryWithCount = await query(`
      SELECT c.*, COUNT(p.id) as product_count 
      FROM categories c 
      LEFT JOIN products p ON c.id = p.category_id 
      WHERE c.id = $1
      GROUP BY c.id
    `, [newCategory.rows[0].id])
    
    console.log(`✅ Category "${categoryWithCount.rows[0].name}" has ${categoryWithCount.rows[0].product_count} products`)

    // Test 6: Test Product with Category and User Info
    console.log('\n6️⃣ Testing Product with Relations...')
    const productWithRelations = await query(`
      SELECT 
        p.name as product_name,
        c.name as category_name,
        u.name as created_by_name
      FROM products p
      JOIN categories c ON p.category_id = c.id
      JOIN users u ON p.created_by = u.id
      WHERE p.category_id = $1
    `, [newCategory.rows[0].id])
    
    if (productWithRelations.rows.length > 0) {
      const product = productWithRelations.rows[0]
      console.log(`✅ Product: "${product.product_name}" in category "${product.category_name}" created by "${product.created_by_name}"`)
    }

    // Test 7: Cleanup Test Data
    console.log('\n7️⃣ Cleaning up test data...')
    await query('DELETE FROM products WHERE category_id = $1', [newCategory.rows[0].id])
    await query('DELETE FROM categories WHERE id = $1', [newCategory.rows[0].id])
    console.log('✅ Test data cleaned up')

    // Test 8: Final Data Verification
    console.log('\n8️⃣ Final Data Verification...')
    const finalCategoryCount = await query('SELECT COUNT(*) as count FROM categories')
    const finalProductCount = await query('SELECT COUNT(*) as count FROM products')
    
    console.log(`✅ Final categories: ${finalCategoryCount.rows[0].count}`)
    console.log(`✅ Final products: ${finalProductCount.rows[0].count}`)

    console.log('\n🎉 Complete workflow test passed successfully!')
    console.log('\n📋 Summary:')
    console.log('   ✅ Database connection working')
    console.log('   ✅ Category creation working')
    console.log('   ✅ Product creation working')
    console.log('   ✅ Relationships working correctly')
    console.log('   ✅ Data integrity maintained')
    
  } catch (error) {
    console.error('❌ Workflow test failed:', error)
    process.exit(1)
  }
}

testCompleteWorkflow()
