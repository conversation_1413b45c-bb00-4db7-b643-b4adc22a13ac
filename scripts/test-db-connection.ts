import { query } from '../src/lib/postgres'

async function testDatabaseConnection() {
  try {
    console.log('🔌 Testing database connection...')
    
    // Test basic connection
    const versionResult = await query('SELECT version()')
    console.log('✅ Database connected successfully')
    console.log('📊 PostgreSQL version:', versionResult.rows[0].version)
    
    // Test tables exist
    const tablesResult = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `)
    
    console.log('\n📋 Tables found:')
    tablesResult.rows.forEach((row: any) => {
      console.log(`   - ${row.table_name}`)
    })
    
    // Test data counts
    const userCount = await query('SELECT COUNT(*) as count FROM users')
    const categoryCount = await query('SELECT COUNT(*) as count FROM categories')
    const productCount = await query('SELECT COUNT(*) as count FROM products')
    
    console.log('\n📊 Data counts:')
    console.log(`   - Users: ${userCount.rows[0].count}`)
    console.log(`   - Categories: ${categoryCount.rows[0].count}`)
    console.log(`   - Products: ${productCount.rows[0].count}`)
    
    // Test sample data
    const sampleCategories = await query('SELECT id, name FROM categories LIMIT 3')
    console.log('\n📂 Sample categories:')
    sampleCategories.rows.forEach((row: any) => {
      console.log(`   - ${row.name} (${row.id})`)
    })
    
    const sampleProducts = await query(`
      SELECT p.name, c.name as category_name 
      FROM products p 
      JOIN categories c ON p.category_id = c.id 
      LIMIT 3
    `)
    console.log('\n🛍️ Sample products:')
    sampleProducts.rows.forEach((row: any) => {
      console.log(`   - ${row.name} (${row.category_name})`)
    })
    
    console.log('\n🎉 Database test completed successfully!')
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
    process.exit(1)
  }
}

testDatabaseConnection()
