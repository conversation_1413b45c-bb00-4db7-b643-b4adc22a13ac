// Simple script to test frontend functionality
// This can be run in the browser console to check for errors

console.log('🧪 Testing frontend functionality...')

// Test 1: Check if API endpoints are accessible
async function testAPIEndpoints() {
  console.log('📡 Testing API endpoints...')
  
  try {
    // Test categories endpoint
    const categoriesResponse = await fetch('/api/categories')
    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json()
      console.log('✅ Categories API working:', categoriesData.categories?.length, 'categories found')
    } else {
      console.error('❌ Categories API failed:', categoriesResponse.status)
    }
    
    // Test products endpoint
    const productsResponse = await fetch('/api/products?limit=5')
    if (productsResponse.ok) {
      const productsData = await productsResponse.json()
      console.log('✅ Products API working:', productsData.products?.length, 'products found')
    } else {
      console.error('❌ Products API failed:', productsResponse.status)
    }
    
    // Test search endpoint
    const searchResponse = await fetch('/api/search?q=phone')
    if (searchResponse.ok) {
      const searchData = await searchResponse.json()
      console.log('✅ Search API working:', searchData.products?.length, 'products found for "phone"')
    } else {
      console.error('❌ Search API failed:', searchResponse.status)
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error)
  }
}

// Test 2: Check for common frontend issues
function testFrontendElements() {
  console.log('🎨 Testing frontend elements...')
  
  // Check if main elements exist
  const header = document.querySelector('header')
  const main = document.querySelector('main') || document.querySelector('[role="main"]')
  const footer = document.querySelector('footer')
  
  console.log('Header found:', !!header)
  console.log('Main content found:', !!main)
  console.log('Footer found:', !!footer)
  
  // Check for images
  const images = document.querySelectorAll('img')
  console.log('Images found:', images.length)
  
  // Check for broken images
  let brokenImages = 0
  images.forEach((img, index) => {
    if (img.naturalWidth === 0 && img.complete) {
      console.warn(`⚠️ Potentially broken image ${index + 1}:`, img.src)
      brokenImages++
    }
  })
  
  if (brokenImages === 0) {
    console.log('✅ No broken images detected')
  } else {
    console.warn(`⚠️ ${brokenImages} potentially broken images found`)
  }
}

// Test 3: Check for console errors
function checkConsoleErrors() {
  console.log('🔍 Monitoring for console errors...')
  
  // Override console.error to catch errors
  const originalError = console.error
  let errorCount = 0
  
  console.error = function(...args) {
    errorCount++
    console.log(`❌ Console Error ${errorCount}:`, ...args)
    originalError.apply(console, args)
  }
  
  // Restore after 5 seconds
  setTimeout(() => {
    console.error = originalError
    if (errorCount === 0) {
      console.log('✅ No console errors detected in the last 5 seconds')
    } else {
      console.log(`⚠️ ${errorCount} console errors detected`)
    }
  }, 5000)
}

// Run all tests
async function runAllTests() {
  await testAPIEndpoints()
  testFrontendElements()
  checkConsoleErrors()
  
  console.log('🎉 Frontend testing complete!')
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  runAllTests()
}

// Export for manual use
if (typeof module !== 'undefined') {
  module.exports = { testAPIEndpoints, testFrontendElements, checkConsoleErrors, runAllTests }
}
