#!/bin/bash

# Test script for price update functionality
# This script tests the price update system end-to-end

API_BASE_URL="http://localhost:3000"
LOG_FILE="logs/test-price-update-$(date +%Y%m%d-%H%M%S).log"

# Create logs directory
mkdir -p logs

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to log errors
log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOG_FILE" >&2
}

log "Starting price update system test"

# Test 1: Check API connectivity
log "Test 1: Checking API connectivity..."
response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/api/products?limit=1")
if [ "$response" = "200" ]; then
    log "✅ API is accessible"
else
    log_error "❌ API not accessible (HTTP $response)"
    exit 1
fi

# Test 2: Check products endpoint
log "Test 2: Testing products endpoint..."
products_response=$(curl -s "$API_BASE_URL/api/products?limit=5")
if echo "$products_response" | grep -q "products"; then
    log "✅ Products endpoint working"
    product_count=$(echo "$products_response" | grep -o '"id"' | wc -l)
    log "Found $product_count products for testing"
else
    log_error "❌ Products endpoint not working properly"
    echo "Response: $products_response"
fi

# Test 3: Check Amazon scraper endpoint
log "Test 3: Testing Amazon scraper endpoint..."
scraper_response=$(curl -s -X POST "$API_BASE_URL/api/scrape-amazon" \
    -H "Content-Type: application/json" \
    -d '{"amazonUrl":"https://amazon.com/dp/B08N5WRWNW"}')

if echo "$scraper_response" | grep -q "price\|error"; then
    log "✅ Amazon scraper endpoint responding"
    if echo "$scraper_response" | grep -q "error"; then
        log "⚠️ Scraper returned error (expected for test URL)"
    fi
else
    log_error "❌ Amazon scraper endpoint not working"
    echo "Response: $scraper_response"
fi

# Test 4: Check authentication (if needed)
log "Test 4: Testing authentication..."
auth_response=$(curl -s -X POST "$API_BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$auth_response" | grep -q "token"; then
    log "✅ Authentication working"
    token=$(echo "$auth_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    log "Got authentication token"
elif echo "$auth_response" | grep -q "error"; then
    log "⚠️ Authentication failed - may need correct credentials"
    log "Response: $auth_response"
else
    log "⚠️ Authentication endpoint may not be implemented"
fi

# Test 5: Test price update script (dry run)
log "Test 5: Testing price update script..."
if [ -f "scripts/update-prices.sh" ]; then
    log "Found price update script, testing dry run..."
    
    # Make script executable
    chmod +x scripts/update-prices.sh
    
    # Run dry run if supported
    if grep -q "dry-run" scripts/update-prices.sh; then
        log "Running dry run test..."
        timeout 30 ./scripts/update-prices.sh --dry-run 2>&1 | head -20 | while read line; do
            log "Script: $line"
        done
    else
        log "⚠️ Dry run not supported in script"
    fi
    
    log "✅ Price update script exists and is executable"
else
    log_error "❌ Price update script not found"
fi

# Test 6: Check required dependencies
log "Test 6: Checking dependencies..."

if command -v curl &> /dev/null; then
    log "✅ curl is available"
else
    log_error "❌ curl is required but not installed"
fi

if command -v jq &> /dev/null; then
    log "✅ jq is available"
else
    log "⚠️ jq is recommended but not installed"
fi

# Test 7: Check server status
log "Test 7: Checking server status..."
server_response=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/")
if [ "$server_response" = "200" ]; then
    log "✅ Server is running and responding"
else
    log_error "❌ Server not responding properly (HTTP $server_response)"
fi

# Summary
log "Test Summary:"
log "============="
log "API Base URL: $API_BASE_URL"
log "Log file: $LOG_FILE"
log ""
log "Next steps to fix price update system:"
log "1. Ensure the server is running on $API_BASE_URL"
log "2. Verify admin credentials for authentication"
log "3. Test Amazon scraping with real product URLs"
log "4. Run the price update script manually: ./scripts/update-prices.sh"
log "5. Check logs in the logs/ directory for detailed error information"
log ""
log "Price update system test completed"
