@echo off
REM Automated Price Update Script for Amazing Deals (Windows Version)
REM This script fetches latest prices from Amazon and updates the database

setlocal enabledelayedexpansion

REM Configuration
set "API_BASE_URL=http://localhost:3000"
set "RATE_LIMIT_DELAY=2"
set "MAX_RETRIES=3"
set "LOG_DIR=logs"
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=!TIMESTAMP: =0!"
set "LOG_FILE=%LOG_DIR%\price-update-!TIMESTAMP!.log"
set "ERROR_LOG=%LOG_DIR%\price-update-errors-!TIMESTAMP!.log"

REM Create logs directory
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Function to log messages
:log
echo [%date% %time%] %~1 >> "%LOG_FILE%"
echo [%date% %time%] %~1
goto :eof

REM Function to log errors
:log_error
echo [%date% %time%] ERROR: %~1 >> "%ERROR_LOG%"
echo [%date% %time%] ERROR: %~1 >&2
goto :eof

REM Check if curl is available
curl --version >nul 2>&1
if errorlevel 1 (
    call :log_error "curl is required but not installed"
    exit /b 1
)

call :log "Starting automated price update process"

REM Get admin token
call :log "Getting admin authentication token..."
curl -s -X POST "%API_BASE_URL%/api/auth/login" ^
    -H "Content-Type: application/json" ^
    -d "{\"email\":\"<EMAIL>\",\"password\":\"admin123\"}" ^
    -o temp_auth.json

if errorlevel 1 (
    call :log_error "Failed to connect to API for authentication"
    exit /b 1
)

REM Note: This is a simplified Windows version
REM For full functionality, consider using PowerShell or WSL
call :log "Windows batch version - limited functionality"
call :log "For full price update functionality, use the Linux/WSL version"
call :log "Or run: wsl ./scripts/update-prices.sh"

REM Cleanup
if exist temp_auth.json del temp_auth.json

call :log "Price update process completed"
call :log "Log file: %LOG_FILE%"

pause
