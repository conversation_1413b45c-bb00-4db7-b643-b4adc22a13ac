#!/bin/bash

# Automated Price Update Script for Amazing Deals
# This script fetches latest prices from Amazon and updates the database
# Can be run manually or scheduled via cron

# Configuration
LOG_FILE="logs/price-update-$(date +%Y%m%d-%H%M%S).log"
ERROR_LOG="logs/price-update-errors-$(date +%Y%m%d-%H%M%S).log"
API_BASE_URL="http://localhost:3000"
RATE_LIMIT_DELAY=2  # seconds between requests
MAX_RETRIES=3
BATCH_SIZE=10

# Create logs directory if it doesn't exist
mkdir -p logs

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to log errors
log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$ERROR_LOG" >&2
}

# Function to check if required tools are available
check_dependencies() {
    log "Checking dependencies..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed"
        exit 1
    fi
    
    log "Dependencies check passed"
}

# Function to get admin token
get_admin_token() {
    log "Getting admin authentication token..."
    
    # Try to get products without authentication first (public endpoint)
    local test_response=$(curl -s -X GET "$API_BASE_URL/api/products?limit=1")

    if [ $? -eq 0 ]; then
        log "API is accessible, proceeding without authentication for read operations"
        echo "no_auth_needed"
        return 0
    fi

    # If authentication is needed, try with admin credentials
    local response=$(curl -s -X POST "$API_BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"admin123"}')
    
    if [ $? -ne 0 ]; then
        log_error "Failed to connect to API for authentication"
        return 1
    fi
    
    local token=$(echo "$response" | jq -r '.token // empty')
    
    if [ -z "$token" ] || [ "$token" = "null" ]; then
        log_error "Failed to get authentication token"
        return 1
    fi
    
    echo "$token"
}

# Function to get all products
get_products() {
    local token=$1
    log "Fetching all products from database..."

    if [ "$token" = "no_auth_needed" ]; then
        local response=$(curl -s -X GET "$API_BASE_URL/api/products?limit=1000")
    else
        local response=$(curl -s -X GET "$API_BASE_URL/api/products?limit=1000" \
            -H "Authorization: Bearer $token")
    fi
    
    if [ $? -ne 0 ]; then
        log_error "Failed to fetch products from API"
        return 1
    fi
    
    echo "$response" | jq -r '.products[]'
}

# Function to scrape price from Amazon
scrape_amazon_price() {
    local amazon_url=$1
    local product_name=$2
    local retry_count=${3:-0}
    
    log "Scraping price for: $product_name"
    
    # Use the existing Amazon scraper API
    local response=$(curl -s -X POST "$API_BASE_URL/api/scrape-amazon" \
        -H "Content-Type: application/json" \
        -d "{\"amazonUrl\":\"$amazon_url\"}")
    
    if [ $? -ne 0 ]; then
        if [ $retry_count -lt $MAX_RETRIES ]; then
            log "Retrying price scrape for $product_name (attempt $((retry_count + 1)))"
            sleep $((RATE_LIMIT_DELAY * 2))
            scrape_amazon_price "$amazon_url" "$product_name" $((retry_count + 1))
            return $?
        else
            log_error "Failed to scrape price for $product_name after $MAX_RETRIES attempts"
            return 1
        fi
    fi
    
    # Extract price from response
    local price=$(echo "$response" | jq -r '.price // empty' | sed 's/[^0-9.]//g')
    
    if [ -z "$price" ] || [ "$price" = "null" ]; then
        log "No price found for $product_name"
        return 1
    fi
    
    echo "$price"
}

# Function to update product price
update_product_price() {
    local token=$1
    local product_id=$2
    local new_price=$3
    local product_name=$4
    
    log "Updating price for $product_name to ₹$new_price"

    if [ "$token" = "no_auth_needed" ]; then
        log_error "Cannot update prices without authentication - this operation requires admin access"
        return 1
    else
        local response=$(curl -s -X PUT "$API_BASE_URL/api/products/$product_id" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $token" \
            -d "{\"price\":$new_price}")
    fi
    
    if [ $? -eq 0 ]; then
        log "Successfully updated price for $product_name"
        return 0
    else
        log_error "Failed to update price for $product_name"
        return 1
    fi
}

# Function to process products in batches
process_products() {
    local token=$1
    local products=$2
    
    local total_products=$(echo "$products" | wc -l)
    local processed=0
    local updated=0
    local errors=0
    
    log "Starting price update for $total_products products"
    
    echo "$products" | while IFS= read -r product; do
        if [ -z "$product" ]; then
            continue
        fi
        
        local product_id=$(echo "$product" | jq -r '.id')
        local product_name=$(echo "$product" | jq -r '.name')
        local amazon_url=$(echo "$product" | jq -r '.amazonAffiliateLink')
        local current_price=$(echo "$product" | jq -r '.price // 0')
        
        processed=$((processed + 1))
        
        log "Processing product $processed/$total_products: $product_name"
        
        # Scrape new price
        new_price=$(scrape_amazon_price "$amazon_url" "$product_name")
        
        if [ $? -eq 0 ] && [ -n "$new_price" ]; then
            # Compare prices (only update if different)
            if [ "$new_price" != "$current_price" ]; then
                if update_product_price "$token" "$product_id" "$new_price" "$product_name"; then
                    updated=$((updated + 1))
                    log "Price updated: $product_name (₹$current_price → ₹$new_price)"
                else
                    errors=$((errors + 1))
                fi
            else
                log "Price unchanged for $product_name (₹$current_price)"
            fi
        else
            errors=$((errors + 1))
            log_error "Failed to get new price for $product_name"
        fi
        
        # Rate limiting
        sleep $RATE_LIMIT_DELAY
        
        # Progress update every 10 products
        if [ $((processed % 10)) -eq 0 ]; then
            log "Progress: $processed/$total_products processed, $updated updated, $errors errors"
        fi
    done
    
    log "Price update completed: $processed processed, $updated updated, $errors errors"
}

# Main execution
main() {
    log "Starting automated price update process"
    
    # Check dependencies
    check_dependencies
    
    # Get authentication token
    token=$(get_admin_token)
    if [ $? -ne 0 ]; then
        log_error "Failed to authenticate. Exiting."
        exit 1
    fi
    
    # Get all products
    products=$(get_products "$token")
    if [ $? -ne 0 ]; then
        log_error "Failed to fetch products. Exiting."
        exit 1
    fi
    
    # Process products
    process_products "$token" "$products"
    
    log "Price update process completed"
    log "Log file: $LOG_FILE"
    log "Error log: $ERROR_LOG"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help|--dry-run]"
        echo "  --help     Show this help message"
        echo "  --dry-run  Show what would be updated without making changes"
        exit 0
        ;;
    --dry-run)
        log "DRY RUN MODE - No changes will be made"
        # Set a flag for dry run mode
        DRY_RUN=true
        ;;
esac

# Run main function
main
