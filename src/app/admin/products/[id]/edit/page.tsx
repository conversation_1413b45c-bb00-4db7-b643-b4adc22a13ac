'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'

interface Category {
  id: string
  name: string
}

interface Product {
  id: string
  name: string
  description: string | null
  amazonAffiliateLink: string
  imageUrl: string | null
  price: number | null
  mrp: number | null
  discountPercentage: number | null
  category: {
    id: string
    name: string
  }
}

export default function EditProductPage() {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [amazonAffiliateLink, setAmazonAffiliateLink] = useState('')
  const [imageUrl, setImageUrl] = useState('')
  const [price, setPrice] = useState('')
  const [mrp, setMrp] = useState('')
  const [discountPercentage, setDiscountPercentage] = useState('')
  const [categoryId, setCategoryId] = useState('')
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [fetchingProduct, setFetchingProduct] = useState(true)
  const [error, setError] = useState('')
  const router = useRouter()
  const params = useParams()
  const productId = params.id as string

  useEffect(() => {
    fetchCategories()
    fetchProduct()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories)
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error)
    }
  }

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/products/${productId}`)
      if (response.ok) {
        const data = await response.json()
        const product: Product = data.product
        
        setName(product.name)
        setDescription(product.description || '')
        setAmazonAffiliateLink(product.amazonAffiliateLink)
        setImageUrl(product.imageUrl || '')
        setPrice(product.price ? product.price.toString() : '')
        setMrp(product.mrp ? product.mrp.toString() : '')
        setDiscountPercentage(product.discountPercentage ? product.discountPercentage.toString() : '')
        setCategoryId(product.category.id)
      } else {
        setError('Product not found')
      }
    } catch (error) {
      setError('Failed to fetch product')
    } finally {
      setFetchingProduct(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      const productData = {
        name,
        description: description || null,
        amazonAffiliateLink,
        imageUrl: imageUrl || null,
        price: price ? parseFloat(price) : null,
        mrp: mrp ? parseFloat(mrp) : null,
        discountPercentage: discountPercentage ? parseInt(discountPercentage) : null,
        categoryId,
      }

      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      })

      if (response.ok) {
        router.push('/admin/products')
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to update product')
      }
    } catch (error) {
      setError('Failed to update product')
    } finally {
      setLoading(false)
    }
  }

  if (fetchingProduct) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (error && fetchingProduct === false && !name) {
    return (
      <div className="px-4 py-6 sm:px-0">
        <div className="text-center py-12">
          <div className="text-red-500 text-lg mb-4">⚠️ {error}</div>
          <Link
            href="/admin/products"
            className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Back to Products
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="md:grid md:grid-cols-3 md:gap-6">
        <div className="md:col-span-1">
          <div className="px-4 sm:px-0">
            <h3 className="text-lg font-medium leading-6 text-gray-900">
              Edit Product
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              Update the product information and Amazon affiliate link.
            </p>
          </div>
        </div>
        <div className="mt-5 md:mt-0 md:col-span-2">
          <form onSubmit={handleSubmit}>
            <div className="shadow sm:rounded-md sm:overflow-hidden">
              <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                    {error}
                  </div>
                )}

                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Product Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    required
                    className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                    placeholder="e.g., iPhone 15 Pro"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea
                    name="description"
                    id="description"
                    rows={3}
                    className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                    placeholder="Product description..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                  />
                </div>

                <div>
                  <label htmlFor="amazonAffiliateLink" className="block text-sm font-medium text-gray-700">
                    Amazon Affiliate Link *
                  </label>
                  <input
                    type="url"
                    name="amazonAffiliateLink"
                    id="amazonAffiliateLink"
                    required
                    className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                    placeholder="https://amazon.com/dp/..."
                    value={amazonAffiliateLink}
                    onChange={(e) => setAmazonAffiliateLink(e.target.value)}
                  />
                </div>

                <div>
                  <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
                    Image URL
                  </label>
                  <input
                    type="url"
                    name="imageUrl"
                    id="imageUrl"
                    className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                    placeholder="https://images.unsplash.com/..."
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                      Current Price (₹)
                    </label>
                    <input
                      type="number"
                      name="price"
                      id="price"
                      step="0.01"
                      min="0"
                      className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                      placeholder="999.99"
                      value={price}
                      onChange={(e) => setPrice(e.target.value)}
                    />
                  </div>

                  <div>
                    <label htmlFor="mrp" className="block text-sm font-medium text-gray-700">
                      MRP (₹)
                    </label>
                    <input
                      type="number"
                      name="mrp"
                      id="mrp"
                      step="0.01"
                      min="0"
                      className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                      placeholder="1199.99"
                      value={mrp}
                      onChange={(e) => setMrp(e.target.value)}
                    />
                  </div>

                  <div>
                    <label htmlFor="discountPercentage" className="block text-sm font-medium text-gray-700">
                      Discount (%)
                    </label>
                    <input
                      type="number"
                      name="discountPercentage"
                      id="discountPercentage"
                      min="0"
                      max="100"
                      className="mt-1 focus:ring-orange-500 focus:border-orange-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500"
                      placeholder="20"
                      value={discountPercentage}
                      onChange={(e) => setDiscountPercentage(e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700">
                    Category *
                  </label>
                  <select
                    name="categoryId"
                    id="categoryId"
                    required
                    className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm text-gray-900"
                    value={categoryId}
                    onChange={(e) => setCategoryId(e.target.value)}
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <Link
                  href="/admin/products"
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 mr-3"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 transition-all duration-200"
                >
                  {loading ? 'Updating...' : 'Update Product'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
