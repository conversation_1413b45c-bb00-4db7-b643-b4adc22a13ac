'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

interface TelegramTestResult {
  success: boolean
  message: string
  connectionTest?: {
    success: boolean
    message: string
  }
  config?: {
    enabled: boolean
    channelId: string
  }
}

export default function TelegramSettingsPage() {
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<TelegramTestResult | null>(null)
  const router = useRouter()

  const handleTestConnection = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/telegram/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()
      setTestResult(result)
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Network error occurred while testing'
      })
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Telegram Integration</h1>
              <p className="mt-2 text-gray-600">
                Configure and test Telegram notifications for new products
              </p>
            </div>
            <button
              onClick={() => router.push('/admin')}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              Back to Admin
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Configuration Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Configuration Status</h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">Environment Variables</h3>
                <p className="text-sm text-gray-600">Required Telegram configuration</p>
              </div>
              <div className="text-right">
                <div className="text-sm space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-600">TELEGRAM_BOT_TOKEN:</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      process.env.NEXT_PUBLIC_TELEGRAM_CONFIGURED === 'true' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {process.env.NEXT_PUBLIC_TELEGRAM_CONFIGURED === 'true' ? 'Set' : 'Not Set'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-600">TELEGRAM_CHANNEL_ID:</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      process.env.NEXT_PUBLIC_TELEGRAM_CONFIGURED === 'true' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {process.env.NEXT_PUBLIC_TELEGRAM_CONFIGURED === 'true' ? 'Set' : 'Not Set'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Setup Instructions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Setup Instructions</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">1. Create Telegram Bot</h3>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Message @BotFather on Telegram</li>
                <li>• Send <code className="bg-gray-100 px-1 rounded">/newbot</code></li>
                <li>• Choose a name and username for your bot</li>
                <li>• Save the bot token provided</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-2">2. Get Channel ID</h3>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Create a Telegram channel or use existing one</li>
                <li>• Add your bot as an administrator</li>
                <li>• Send a test message to the channel</li>
                <li>• Visit: <code className="bg-gray-100 px-1 rounded">https://api.telegram.org/bot&lt;YOUR_BOT_TOKEN&gt;/getUpdates</code></li>
                <li>• Find the channel ID in the response</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-2">3. Update Environment Variables</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-sm text-gray-800">
{`# Add to .env.local
TELEGRAM_BOT_TOKEN="your-telegram-bot-token-here"
TELEGRAM_CHANNEL_ID="@your-channel-username-or-chat-id"
TELEGRAM_NOTIFICATIONS_ENABLED="true"`}
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Test Connection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Integration</h2>
          
          <div className="space-y-4">
            <p className="text-gray-600">
              Test your Telegram integration by sending a sample notification to your configured channel.
            </p>

            <button
              onClick={handleTestConnection}
              disabled={testing}
              className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Testing...</span>
                </>
              ) : (
                <span>Send Test Notification</span>
              )}
            </button>

            {testResult && (
              <div className={`p-4 rounded-lg ${
                testResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                    testResult.success ? 'bg-green-500' : 'bg-red-500'
                  }`}>
                    {testResult.success ? (
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-medium ${
                      testResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {testResult.success ? 'Test Successful!' : 'Test Failed'}
                    </h3>
                    <p className={`text-sm mt-1 ${
                      testResult.success ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {testResult.message}
                    </p>
                    
                    {testResult.connectionTest && (
                      <div className="mt-2 text-sm">
                        <strong>Connection:</strong> {testResult.connectionTest.message}
                      </div>
                    )}
                    
                    {testResult.config && (
                      <div className="mt-2 text-sm">
                        <strong>Config:</strong> Enabled: {testResult.config.enabled ? 'Yes' : 'No'}, 
                        Channel: {testResult.config.channelId}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Notification Features</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">✅ Automatic Notifications</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• New product additions</li>
                <li>• Bulk product imports</li>
                <li>• Product updates (optional)</li>
                <li>• Rich formatting with emojis</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">📱 Message Format</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Product name and description</li>
                <li>• Price and discount information</li>
                <li>• Category and affiliate links</li>
                <li>• Product images when available</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
