'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function WhatsAppManagementPage() {
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const router = useRouter()

  const handleTestConnection = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      // This would be implemented when WhatsApp API is available
      setTestResult({
        success: false,
        message: 'WhatsApp Business API configuration required'
      })
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Network error occurred while testing'
      })
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">📱 WhatsApp Business</h1>
              <p className="mt-2 text-gray-600">
                Configure and manage WhatsApp Business API integration
              </p>
            </div>
            <button
              onClick={() => router.push('/admin')}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              Back to Admin
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Configuration Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Configuration Status</h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">WhatsApp Business API</h3>
                <p className="text-sm text-gray-600">Business Account and API configuration</p>
              </div>
              <div className="text-right">
                <span className="px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800">
                  Configuration Required
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Setup Instructions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Setup Instructions</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">1. WhatsApp Business Account</h3>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Create a WhatsApp Business Account</li>
                <li>• Apply for WhatsApp Business API access</li>
                <li>• Get approval from WhatsApp</li>
                <li>• Obtain Business Account ID</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-2">2. Facebook Developer Account</h3>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Create Facebook Developer account</li>
                <li>• Create a new app for WhatsApp Business</li>
                <li>• Add WhatsApp Business product</li>
                <li>• Generate access token</li>
                <li>• Get Phone Number ID</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-2">3. Environment Variables</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-sm text-gray-800">
{`# Add to .env.local
WHATSAPP_BUSINESS_ACCOUNT_ID="your_business_account_id"
WHATSAPP_ACCESS_TOKEN="your_access_token"
WHATSAPP_PHONE_NUMBER_ID="your_phone_number_id"
WHATSAPP_WEBHOOK_VERIFY_TOKEN="your_verify_token"`}
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Test Connection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Integration</h2>
          
          <div className="space-y-4">
            <p className="text-gray-600">
              Test your WhatsApp Business API integration by verifying the connection.
            </p>

            <button
              onClick={handleTestConnection}
              disabled={testing}
              className="bg-emerald-500 text-white px-6 py-3 rounded-lg hover:bg-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Testing...</span>
                </>
              ) : (
                <span>Test WhatsApp Connection</span>
              )}
            </button>

            {testResult && (
              <div className={`p-4 rounded-lg ${
                testResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-yellow-50 border border-yellow-200'
              }`}>
                <div className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                    testResult.success ? 'bg-green-500' : 'bg-yellow-500'
                  }`}>
                    {testResult.success ? (
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-medium ${
                      testResult.success ? 'text-green-800' : 'text-yellow-800'
                    }`}>
                      {testResult.success ? 'Connection Successful!' : 'Configuration Required'}
                    </h3>
                    <p className={`text-sm mt-1 ${
                      testResult.success ? 'text-green-700' : 'text-yellow-700'
                    }`}>
                      {testResult.message}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">WhatsApp Features</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">📱 Automated Notifications</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• New product announcements</li>
                <li>• Price drop alerts</li>
                <li>• Daily deals summaries</li>
                <li>• Bulk campaign messages</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">👥 Subscriber Management</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Opt-in/opt-out handling</li>
                <li>• Subscriber list management</li>
                <li>• Message delivery tracking</li>
                <li>• Compliance with WhatsApp policies</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">📝 Message Templates</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Pre-approved message templates</li>
                <li>• Product promotion formats</li>
                <li>• Amazing Deals branding</li>
                <li>• Variable substitution</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">📊 Analytics & Tracking</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Message delivery status</li>
                <li>• Read receipts tracking</li>
                <li>• Campaign performance</li>
                <li>• Subscriber engagement</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
