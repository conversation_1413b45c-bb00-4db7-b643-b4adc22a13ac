import { NextRequest, NextResponse } from 'next/server'
import { findBannerById, updateBanner, deleteBanner } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware'
import { z } from 'zod'

const updateBannerSchema = z.object({
  title: z.string().min(1, 'Banner title is required').optional(),
  imageUrl: z.string().url('Invalid image URL').optional(),
  linkUrl: z.string().url('Invalid link URL').optional().or(z.literal('')).or(z.undefined()),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isActive: z.boolean().optional(),
  displayOrder: z.number().int().min(0).optional(),
})

// GET /api/banners/[id] - Get banner by ID (public)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const banner = await findBannerById(resolvedParams.id)
    
    if (!banner) {
      return NextResponse.json(
        { error: 'Banner not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ banner })
  } catch (error) {
    console.error('Get banner error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch banner' },
      { status: 500 }
    )
  }
}

// PUT /api/banners/[id] - Update banner (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const resolvedParams = await params
      const body = await req.json()
      const validatedData = updateBannerSchema.parse(body)

      // Check if banner exists
      const existingBanner = await findBannerById(resolvedParams.id)
      if (!existingBanner) {
        return NextResponse.json(
          { error: 'Banner not found' },
          { status: 404 }
        )
      }

      // Prepare update data
      const updateData: any = {}
      if (validatedData.title !== undefined) updateData.title = validatedData.title
      if (validatedData.imageUrl !== undefined) updateData.image_url = validatedData.imageUrl
      if (validatedData.linkUrl !== undefined) updateData.link_url = validatedData.linkUrl || null
      if (validatedData.startDate !== undefined) updateData.start_date = new Date(validatedData.startDate)
      if (validatedData.endDate !== undefined) updateData.end_date = validatedData.endDate ? new Date(validatedData.endDate) : null
      if (validatedData.isActive !== undefined) updateData.is_active = validatedData.isActive
      if (validatedData.displayOrder !== undefined) updateData.display_order = validatedData.displayOrder

      const banner = await updateBanner(resolvedParams.id, updateData)

      return NextResponse.json({ banner })
    } catch (error) {
      console.error('Update banner error:', error)

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.issues },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to update banner' },
        { status: 500 }
      )
    }
  })
}

// DELETE /api/banners/[id] - Delete banner (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const resolvedParams = await params
      // Check if banner exists
      const existingBanner = await findBannerById(resolvedParams.id)
      if (!existingBanner) {
        return NextResponse.json(
          { error: 'Banner not found' },
          { status: 404 }
        )
      }

      const success = await deleteBanner(resolvedParams.id)
      
      if (!success) {
        return NextResponse.json(
          { error: 'Failed to delete banner' },
          { status: 500 }
        )
      }

      return NextResponse.json({ message: 'Banner deleted successfully' })
    } catch (error) {
      console.error('Delete banner error:', error)
      return NextResponse.json(
        { error: 'Failed to delete banner' },
        { status: 500 }
      )
    }
  })
}
