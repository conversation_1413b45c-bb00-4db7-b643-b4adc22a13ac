import { NextRequest, NextResponse } from 'next/server'
import { getAllBanners, getAllBannersAdmin, createBanner } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware'
import { z } from 'zod'

const bannerSchema = z.object({
  title: z.string().min(1, 'Banner title is required'),
  imageUrl: z.string().url('Invalid image URL'),
  linkUrl: z.string().url('Invalid link URL').optional().or(z.literal('')).or(z.undefined()),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isActive: z.boolean().default(true),
  displayOrder: z.number().int().min(0).default(0),
})

// GET /api/banners - Get active banners (public)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const admin = searchParams.get('admin') === 'true'
    
    let banners
    if (admin) {
      // Admin route - get all banners
      banners = await getAllBannersAdmin()
    } else {
      // Public route - get only active banners
      banners = await getAllBanners()
    }

    return NextResponse.json({ banners })
  } catch (error) {
    console.error('Get banners error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch banners' },
      { status: 500 }
    )
  }
}

// POST /api/banners - Create new banner (admin only)
export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const body = await req.json()
      const validatedData = bannerSchema.parse(body)

      const banner = await createBanner({
        title: validatedData.title,
        image_url: validatedData.imageUrl,
        link_url: validatedData.linkUrl || undefined,
        start_date: validatedData.startDate ? new Date(validatedData.startDate) : undefined,
        end_date: validatedData.endDate ? new Date(validatedData.endDate) : undefined,
        is_active: validatedData.isActive,
        display_order: validatedData.displayOrder,
        created_by: user.id
      })

      return NextResponse.json({ banner }, { status: 201 })
    } catch (error) {
      console.error('Create banner error:', error)

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.issues },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create banner' },
        { status: 500 }
      )
    }
  })
}
