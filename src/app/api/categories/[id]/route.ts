import { NextRequest, NextResponse } from 'next/server'
import { findCategoryById, updateCategory, deleteCategory, getAllProducts } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware'
import { categorySchema } from '@/lib/validations'

// GET /api/categories/[id] - Get single category (public)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const category = await findCategoryById(id)

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Get products for this category
    const { products, total } = await getAllProducts({ categoryId: id })

    const categoryWithProducts = {
      ...category,
      products,
      _count: { products: total }
    }

    return NextResponse.json({ category: categoryWithProducts })
  } catch (error) {
    console.error('Get category error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    )
  }
}

// PUT /api/categories/[id] - Update category (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAdminAuth(request, async (req, _user) => {
    try {
      const { id } = await params
      const body = await req.json()
      const validatedData = categorySchema.parse(body)

      // Check if category exists
      const existingCategory = await findCategoryById(id)

      if (!existingCategory) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        )
      }

      const category = await updateCategory(id, validatedData)

      return NextResponse.json({ category })
    } catch (error) {
      console.error('Update category error:', error)

      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to update category' },
        { status: 500 }
      )
    }
  })
}

// DELETE /api/categories/[id] - Delete category (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAdminAuth(request, async (_req, _user) => {
    try {
      const { id } = await params
      // Check if category exists
      const existingCategory = await findCategoryById(id)

      if (!existingCategory) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        )
      }

      // Check if category has products
      const { total } = await getAllProducts({ categoryId: id })
      if (total > 0) {
        return NextResponse.json(
          { error: 'Cannot delete category with existing products' },
          { status: 409 }
        )
      }

      await deleteCategory(id)

      return NextResponse.json({ message: 'Category deleted successfully' })
    } catch (error) {
      console.error('Delete category error:', error)
      return NextResponse.json(
        { error: 'Failed to delete category' },
        { status: 500 }
      )
    }
  })
}
