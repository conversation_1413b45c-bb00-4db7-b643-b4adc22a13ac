import { NextRequest, NextResponse } from 'next/server'
import { getCategoriesWithProductCount, createCategory, findCategoryBySlug } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware'
import { categorySchema } from '@/lib/validations'

// GET /api/categories - Get all categories (public)
export async function GET() {
  try {
    const categories = await getCategoriesWithProductCount()

    return NextResponse.json({ categories })
  } catch (error) {
    console.error('Get categories error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}

// POST /api/categories - Create new category (admin only)
export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, _user) => {
    try {
      const body = await req.json()
      const validatedData = categorySchema.parse(body)

      // Check if category with same slug already exists
      const existingCategory = await findCategoryBySlug(validatedData.slug)

      if (existingCategory) {
        return NextResponse.json(
          { error: 'Category with this slug already exists' },
          { status: 409 }
        )
      }

      const category = await createCategory(validatedData)

      return NextResponse.json({ category }, { status: 201 })
    } catch (error) {
      console.error('Create category error:', error)

      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create category' },
        { status: 500 }
      )
    }
  })
}
