import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email'
import { withAdminAuth } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const campaigns = await emailService.getCampaigns()
      return NextResponse.json({ campaigns })
    } catch (error) {
      console.error('Failed to get campaigns:', error)
      return NextResponse.json(
        { error: 'Failed to get campaigns' },
        { status: 500 }
      )
    }
  })
}

export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
  try {
    const { name, subject, htmlContent, templateId, action } = await request.json()

    if (action === 'send') {
      // Send existing campaign
      const { campaignId } = await request.json()
      
      if (!campaignId) {
        return NextResponse.json(
          { error: 'Campaign ID is required for sending' },
          { status: 400 }
        )
      }

      const success = await emailService.sendCampaign(campaignId)
      
      if (success) {
        return NextResponse.json({ message: 'Campaign sent successfully' })
      } else {
        return NextResponse.json(
          { error: 'Failed to send campaign' },
          { status: 500 }
        )
      }
    } else {
      // Create new campaign
      if (!name || !subject || !htmlContent) {
        return NextResponse.json(
          { error: 'Name, subject, and HTML content are required' },
          { status: 400 }
        )
      }

      const campaignId = await emailService.createCampaign(name, subject, htmlContent, templateId)
      
      if (campaignId) {
        return NextResponse.json({ 
          message: 'Campaign created successfully',
          campaignId 
        })
      } else {
        return NextResponse.json(
          { error: 'Failed to create campaign' },
          { status: 500 }
        )
      }
    }
    } catch (error) {
      console.error('Failed to process campaign request:', error)
      return NextResponse.json(
        { error: 'Failed to process campaign request' },
        { status: 500 }
      )
    }
  })
}
