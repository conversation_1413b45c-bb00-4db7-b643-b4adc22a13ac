import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email'
import { withAdminAuth } from '@/lib/middleware'

export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
  try {
    const { subscribers } = await request.json()

    if (!subscribers || !Array.isArray(subscribers)) {
      return NextResponse.json(
        { error: 'Subscribers array is required' },
        { status: 400 }
      )
    }

    // Validate email format for all subscribers
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const validSubscribers = subscribers.filter(sub => 
      sub.email && emailRegex.test(sub.email)
    )

    if (validSubscribers.length === 0) {
      return NextResponse.json(
        { error: 'No valid email addresses found' },
        { status: 400 }
      )
    }

    const result = await emailService.importSubscribers(validSubscribers)
    
    return NextResponse.json({
      message: 'Import completed',
      total: subscribers.length,
      valid: validSubscribers.length,
      success: result.success,
      failed: result.failed
    })
    } catch (error) {
      console.error('Failed to import subscribers:', error)
      return NextResponse.json(
        { error: 'Failed to import subscribers' },
        { status: 500 }
      )
    }
  })
}
