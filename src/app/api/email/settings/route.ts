import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email'
import { withAdminAuth } from '@/lib/middleware'

export async function POST(request: NextRequest) {
  return withAdmin<PERSON>uth(request, async (req, user) => {
  try {
    const { action, ...settings } = await request.json()

    if (action === 'test') {
      // Test email configuration
      const { testEmail } = await request.json()
      
      if (!testEmail) {
        return NextResponse.json(
          { error: 'Test email address is required' },
          { status: 400 }
        )
      }

      const connectionTest = await emailService.testConnection()
      
      if (!connectionTest.success) {
        return NextResponse.json({
          success: false,
          message: connectionTest.message
        })
      }

      const testSent = await emailService.sendTestEmail(testEmail)
      
      return NextResponse.json({
        success: testSent,
        message: testSent 
          ? 'Test email sent successfully!' 
          : 'Failed to send test email',
        connectionTest
      })
    } else {
      // Update email settings
      const success = await emailService.updateEmailSettings(settings)
      
      if (success) {
        return NextResponse.json({ message: 'Email settings updated successfully' })
      } else {
        return NextResponse.json(
          { error: 'Failed to update email settings' },
          { status: 500 }
        )
      }
    }
    } catch (error) {
      console.error('Failed to process email settings request:', error)
      return NextResponse.json(
        { error: 'Failed to process email settings request' },
        { status: 500 }
      )
    }
  })
}
