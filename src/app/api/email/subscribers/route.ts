import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email'
import { withAdminAuth } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const subscribers = await emailService.getSubscribers()
      return NextResponse.json({ subscribers })
    } catch (error) {
      console.error('Failed to get subscribers:', error)
      return NextResponse.json(
        { error: 'Failed to get subscribers' },
        { status: 500 }
      )
    }
  })
}

export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
  try {
    const { email, name, source = 'manual' } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const success = await emailService.addSubscriber(email, name, source)
    
    if (success) {
      return NextResponse.json({ message: 'Subscriber added successfully' })
    } else {
      return NextResponse.json(
        { error: 'Failed to add subscriber' },
        { status: 500 }
      )
    }
    } catch (error) {
      console.error('Failed to add subscriber:', error)
      return NextResponse.json(
        { error: 'Failed to add subscriber' },
        { status: 500 }
      )
    }
  })
}

export async function DELETE(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    const success = await emailService.removeSubscriber(email)
    
    if (success) {
      return NextResponse.json({ message: 'Subscriber removed successfully' })
    } else {
      return NextResponse.json(
        { error: 'Failed to remove subscriber' },
        { status: 500 }
      )
    }
    } catch (error) {
      console.error('Failed to remove subscriber:', error)
      return NextResponse.json(
        { error: 'Failed to remove subscriber' },
        { status: 500 }
      )
    }
  })
}
