import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email'
import { withAdminAuth } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const templates = await emailService.getTemplates()
      return NextResponse.json({ templates })
    } catch (error) {
      console.error('Failed to get templates:', error)
      return NextResponse.json(
        { error: 'Failed to get templates' },
        { status: 500 }
      )
    }
  })
}

export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
  try {
    const { name, subject, htmlContent, templateType = 'custom' } = await request.json()

    if (!name || !subject || !htmlContent) {
      return NextResponse.json(
        { error: 'Name, subject, and HTML content are required' },
        { status: 400 }
      )
    }

    const templateId = await emailService.createTemplate(name, subject, htmlContent, templateType)
    
    if (templateId) {
      return NextResponse.json({ 
        message: 'Template created successfully',
        templateId 
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to create template' },
        { status: 500 }
      )
    }
    } catch (error) {
      console.error('Failed to create template:', error)
      return NextResponse.json(
        { error: 'Failed to create template' },
        { status: 500 }
      )
    }
  })
}
