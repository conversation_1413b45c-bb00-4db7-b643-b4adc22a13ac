import { NextRequest, NextResponse } from 'next/server'
import { findProductById, updateProduct, deleteProduct, findCategoryById } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware'
import { updateProductSchema } from '@/lib/validations'

// GET /api/products/[id] - Get single product (public)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const product = await findProductById(id)

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ product })
  } catch (error) {
    console.error('Get product error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    )
  }
}

// PUT /api/products/[id] - Update product (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAdminAuth(request, async (req, _user) => {
    try {
      const { id } = await params
      const body = await req.json()
      const validatedData = updateProductSchema.parse(body)

      // Check if product exists
      const existingProduct = await findProductById(id)

      if (!existingProduct) {
        return NextResponse.json(
          { error: 'Product not found' },
          { status: 404 }
        )
      }

      // If categoryId is being updated, check if category exists
      if (validatedData.categoryId) {
        const category = await findCategoryById(validatedData.categoryId)

        if (!category) {
          return NextResponse.json(
            { error: 'Category not found' },
            { status: 404 }
          )
        }
      }

      // Map the validated data to database field names
      const updateData: Record<string, any> = {}
      if (validatedData.name !== undefined) updateData.name = validatedData.name
      if (validatedData.description !== undefined) updateData.description = validatedData.description
      if (validatedData.amazonAffiliateLink !== undefined) updateData.amazon_affiliate_link = validatedData.amazonAffiliateLink
      if (validatedData.imageUrl !== undefined) updateData.image_url = validatedData.imageUrl
      if (validatedData.price !== undefined) updateData.price = validatedData.price
      if (validatedData.categoryId !== undefined) updateData.category_id = validatedData.categoryId

      const product = await updateProduct(id, updateData)
      
      return NextResponse.json({ product })
    } catch (error) {
      console.error('Update product error:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data' },
          { status: 400 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to update product' },
        { status: 500 }
      )
    }
  })
}

// DELETE /api/products/[id] - Delete product (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAdminAuth(request, async (_req, _user) => {
    try {
      const { id } = await params
      // Check if product exists
      const existingProduct = await findProductById(id)

      if (!existingProduct) {
        return NextResponse.json(
          { error: 'Product not found' },
          { status: 404 }
        )
      }

      await deleteProduct(id)
      
      return NextResponse.json({ message: 'Product deleted successfully' })
    } catch (error) {
      console.error('Delete product error:', error)
      return NextResponse.json(
        { error: 'Failed to delete product' },
        { status: 500 }
      )
    }
  })
}
