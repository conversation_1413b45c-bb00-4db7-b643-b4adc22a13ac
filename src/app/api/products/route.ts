import { NextRequest, NextResponse } from 'next/server'
import { getAllProducts, createProduct, findCategoryById, checkDuplicateProduct } from '@/lib/database'
import { withAdminAuth } from '@/lib/middleware'
import { productSchema } from '@/lib/validations'
import { telegramService } from '@/lib/telegram'

// GET /api/products - Get all products with optional filtering (public)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    const { products, total } = await getAllProducts({
      categoryId: category || undefined,
      search: search || undefined,
      limit,
      offset
    })

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get products error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}

// POST /api/products - Create new product (admin only)
export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
    try {
      const body = await req.json()
      const validatedData = productSchema.parse(body)

      // Check if category exists
      const category = await findCategoryById(validatedData.categoryId)

      if (!category) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        )
      }

      // Check for duplicate products with enhanced detection
      const existingProduct = await checkDuplicateProduct(
        validatedData.name,
        validatedData.amazonAffiliateLink,
        validatedData.description,
        validatedData.imageUrl,
        validatedData.price,
        validatedData.categoryId
      )

      if (existingProduct) {
        const reasons = existingProduct.duplicateReasons || ['Unknown criteria']
        return NextResponse.json(
          {
            error: 'Duplicate product detected',
            existingProduct,
            duplicateReasons: reasons,
            message: `Product matches existing product "${existingProduct.name}" based on: ${reasons.join(', ')}`
          },
          { status: 409 }
        )
      }

      const product = await createProduct({
        name: validatedData.name,
        description: validatedData.description,
        amazon_affiliate_link: validatedData.amazonAffiliateLink,
        image_url: validatedData.imageUrl,
        price: validatedData.price,
        mrp: validatedData.mrp,
        discount_percentage: validatedData.discountPercentage,
        category_id: validatedData.categoryId,
        created_by: user.id
      })

      // Send notifications to all channels (non-blocking)
      const productData = {
        id: product.id,
        name: product.name,
        description: product.description || undefined,
        price: product.price || undefined,
        mrp: product.mrp || undefined,
        discountPercentage: product.discount_percentage || undefined,
        category: category.name,
        amazonAffiliateLink: product.amazon_affiliate_link,
        imageUrl: product.image_url || undefined
      }

      // Telegram notification
      try {
        await telegramService.notifyNewProduct(productData)
      } catch (error) {
        console.error('Telegram notification failed:', error)
      }

      // Additional notification services can be added here
      // WhatsApp and Email notifications would be triggered separately

      return NextResponse.json({ product }, { status: 201 })
    } catch (error) {
      console.error('Create product error:', error)

      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create product' },
        { status: 500 }
      )
    }
  })
}
