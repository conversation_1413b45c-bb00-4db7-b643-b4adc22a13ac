import { NextRequest, NextResponse } from 'next/server'
import { AmazonScraper } from '@/lib/amazon-scraper'

export async function POST(request: NextRequest) {
  try {
    const { amazonUrl } = await request.json()

    if (!amazonUrl) {
      return NextResponse.json(
        { error: 'Amazon URL is required' },
        { status: 400 }
      )
    }

    // Validate Amazon URL
    if (!AmazonScraper.isValidAmazonUrl(amazonUrl)) {
      return NextResponse.json(
        { error: 'Invalid Amazon URL' },
        { status: 400 }
      )
    }

    // Inject affiliate tag into the URL
    const urlWithAffiliateTag = AmazonScraper.injectAffiliateTag(amazonUrl)

    // Scrape product data (supports both single and multiple products)
    const scrapingResult = await AmazonScraper.scrapeProducts(urlWithAffiliateTag)

    if (!scrapingResult || scrapingResult.products.length === 0) {
      return NextResponse.json(
        { error: 'Failed to scrape product data. The page might be protected or unavailable.' },
        { status: 400 }
      )
    }

    // Add affiliate tag to all product URLs
    const productsWithAffiliateTags = scrapingResult.products.map(product => ({
      ...product,
      originalUrl: product.url ? AmazonScraper.injectAffiliateTag(product.url) : urlWithAffiliateTag,
      amazonAffiliateLink: product.url ? AmazonScraper.injectAffiliateTag(product.url) : urlWithAffiliateTag
    }))

    return NextResponse.json({
      success: true,
      isMultiProduct: scrapingResult.isMultiProduct,
      totalFound: scrapingResult.totalFound,
      data: scrapingResult.isMultiProduct ? productsWithAffiliateTags : productsWithAffiliateTags[0],
      products: productsWithAffiliateTags // Always include array for consistency
    })

  } catch (error) {
    console.error('Amazon scraping error:', error)
    return NextResponse.json(
      { error: 'Internal server error while scraping Amazon data' },
      { status: 500 }
    )
  }
}
