import { NextRequest, NextResponse } from 'next/server'
import { telegramService } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const { products } = await request.json()

    if (!products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { error: 'Products array is required' },
        { status: 400 }
      )
    }

    // Send bulk import notification
    const success = await telegramService.notifyBulkImport(products, 'Amazon')

    return NextResponse.json({ 
      success,
      message: success ? 'Bulk import notification sent' : 'Notification failed'
    })
  } catch (error) {
    console.error('Bulk import notification error:', error)
    return NextResponse.json(
      { error: 'Failed to send notification' },
      { status: 500 }
    )
  }
}
