import { NextRequest, NextResponse } from 'next/server'
import { telegramService } from '@/lib/telegram'
import { withAdminAuth } from '@/lib/middleware'

export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req, user) => {
  try {
    // Test connection
    const connectionTest = await telegramService.testConnection()
    
    if (!connectionTest.success) {
      return NextResponse.json({
        success: false,
        message: connectionTest.message,
        config: telegramService.getConfig()
      })
    }

    // Send test notification
    const testProduct = {
      id: 'test-123',
      name: '🧪 Test Product - Amazing Deals Integration',
      description: 'This is a test notification to verify Telegram integration is working correctly.',
      price: 999,
      mrp: 1299,
      discountPercentage: 23,
      category: 'Test Category',
      amazonAffiliateLink: 'https://amazon.in/test-product',
      imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop'
    }

    const notificationSent = await telegramService.notifyNewProduct(testProduct)

    return NextResponse.json({
      success: notificationSent,
      message: notificationSent 
        ? 'Test notification sent successfully!' 
        : 'Failed to send test notification',
      connectionTest,
      config: telegramService.getConfig()
    })
    } catch (error) {
      console.error('Telegram test error:', error)
      return NextResponse.json(
        {
          success: false,
          message: 'Test failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  })
}
