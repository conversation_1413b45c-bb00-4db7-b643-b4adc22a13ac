@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Custom utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Disable dark mode for consistent form visibility */
body {
  background: #ffffff;
  color: #171717;
  font-family: Arial, Helvetica, sans-serif;
}

/* Ensure form inputs have proper visibility */
input, textarea, select {
  color: #111827 !important;
  background-color: #ffffff !important;
}

input::placeholder, textarea::placeholder {
  color: #6b7280 !important;
}

/* Ensure labels are visible */
label {
  color: #374151 !important;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modern UI Enhancements */
.card-modern {
  @apply bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-100;
}

.card-modern:hover {
  transform: translateY(-2px);
}

.btn-primary {
  @apply bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105;
}

.btn-secondary {
  @apply bg-white hover:bg-gray-50 text-gray-700 font-semibold py-3 px-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200;
}

.input-modern {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white text-gray-900 placeholder-gray-500;
}

.input-modern:focus {
  box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.1);
}

/* Loading animations */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Micro-interactions */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-1px);
}

/* Enhanced shadows */
.shadow-modern {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-modern-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Typography improvements */
.text-gradient {
  background: linear-gradient(135deg, #f97316, #ea580c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Grid improvements */
.grid-modern {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.grid-compact {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

@media (max-width: 640px) {
  .grid-modern {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  .grid-compact {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .grid-modern {
    grid-template-columns: repeat(3, 1fr);
  }
  .grid-compact {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1025px) {
  .grid-modern {
    grid-template-columns: repeat(4, 1fr);
  }
  .grid-compact {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
