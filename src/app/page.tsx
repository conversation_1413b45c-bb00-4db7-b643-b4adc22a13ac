'use client'

import { useEffect, useState, useCallback } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import AmazonButton from '@/components/AmazonButton'
import ProductDetailModal from '@/components/ProductDetailModal'

import CleanHeader from '@/components/CleanHeader'
import CleanFooter from '@/components/CleanFooter'
import CleanProductCard from '@/components/CleanProductCard'

import { LoadingGrid } from '@/components/LoadingSpinner'

interface Product {
  id: string
  name: string
  description: string | null
  amazonAffiliateLink: string
  imageUrl: string | null
  price: number | null
  category: {
    id: string
    name: string
    slug: string
  }
}

interface Category {
  id: string
  name: string
  slug: string
  _count: {
    products: number
  }
}

export default function Home() {
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isFiltered, setIsFiltered] = useState(false)
  const [groupedProducts, setGroupedProducts] = useState<{[key: string]: Product[]}>({})
  const [showCategoryGroups, setShowCategoryGroups] = useState(true)

  const fetchData = async () => {
    try {
      const categoriesRes = await fetch('/api/categories')
      const categoriesData = await categoriesRes.json()
      setCategories(categoriesData.categories || [])
    } catch (error) {
      console.error('Failed to fetch categories:', error)
    }
  }

  const fetchProducts = useCallback(async () => {
    try {
      const params = new URLSearchParams()
      if (selectedCategory) params.append('category', selectedCategory)
      if (searchQuery) params.append('search', searchQuery)

      // Fetch more products for category grouping
      params.append('limit', selectedCategory || searchQuery ? '24' : '48')

      const response = await fetch(`/api/products?${params}`)
      const data = await response.json()
      const fetchedProducts = data.products || []
      setProducts(fetchedProducts)

      // Group products by category when no specific category is selected
      if (!selectedCategory && !searchQuery) {
        const grouped = fetchedProducts.reduce((acc: {[key: string]: Product[]}, product: Product) => {
          const categoryName = product.category.name
          if (!acc[categoryName]) {
            acc[categoryName] = []
          }
          acc[categoryName].push(product)
          return acc
        }, {})
        setGroupedProducts(grouped)
        setShowCategoryGroups(true)
      } else {
        setShowCategoryGroups(false)
      }
    } catch (error) {
      console.error('Failed to fetch products:', error)
    } finally {
      setLoading(false)
    }
  }, [selectedCategory, searchQuery])

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setIsFiltered(true)
    fetchProducts()
  }

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setIsFiltered(categoryId !== '')
    fetchProducts()
  }

  const clearFilters = () => {
    setSelectedCategory('')
    setSearchQuery('')
    setIsFiltered(false)
    fetchProducts()
  }

  const handleViewDetails = (productId: string) => {
    setSelectedProductId(productId)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedProductId(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50">
      {/* Clean Header */}
      <CleanHeader
        categories={categories}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onSearchSubmit={handleSearch}
        onCategorySelect={handleCategorySelect}
        selectedCategory={selectedCategory}
      />




      





      {/* Filter Indicator and Products */}
      <div className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Filter Status */}
          {isFiltered && (
            <div className="mb-8 bg-orange-50 border border-orange-200 rounded-xl p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-orange-600 font-semibold">Filtered by:</span>
                    {selectedCategory && (
                      <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {categories.find(c => c.id === selectedCategory)?.name || 'Category'}
                      </span>
                    )}
                    {searchQuery && (
                      <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        "{searchQuery}"
                      </span>
                    )}
                  </div>
                  <span className="text-gray-600">({products.length} products found)</span>
                </div>
                <button
                  onClick={clearFilters}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}

          {/* Products Section */}
          <div className="mb-12">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h2 className="text-4xl font-bold text-gray-900 mb-2">
                  {isFiltered ? 'Filtered Products' : 'Latest Products'}
                </h2>
                <p className="text-xl text-gray-600">
                  {isFiltered ? 'Products matching your criteria' : 'Discover our newest additions'}
                </p>
              </div>
              {!isFiltered && (
                <button
                  onClick={() => handleCategorySelect('')}
                  className="bg-gradient-to-r from-orange-400 to-orange-500 text-white px-6 py-3 rounded-xl font-semibold hover:from-orange-500 hover:to-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  View All Products
                </button>
              )}
            </div>
          </div>



        {/* Products Grid */}
        <div id="products">
          {loading ? (
            <LoadingGrid count={12} />
          ) : showCategoryGroups ? (
            // Category-based organization
            <div className="space-y-12">
              {Object.entries(groupedProducts).map(([categoryName, categoryProducts]) => (
                <div key={categoryName} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-gray-900">{categoryName}</h3>
                    <span className="text-sm text-gray-500">{categoryProducts.length} products</span>
                  </div>
                  <div className="grid-compact">
                    {categoryProducts.slice(0, 12).map((product) => (
                      <CleanProductCard
                        key={product.id}
                        product={product}
                        onViewDetails={handleViewDetails}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Regular grid when filtered
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid-compact">
                {products.map((product) => (
                  <CleanProductCard
                    key={product.id}
                    product={product}
                    onViewDetails={handleViewDetails}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

          {products.length === 0 && !loading && (
            <div className="text-center py-20">
              <div className="text-8xl mb-6">🔍</div>
              <h3 className="text-3xl font-bold text-gray-900 mb-4">No products found</h3>
              <p className="text-xl text-gray-600 mb-8 max-w-md mx-auto">
                We couldn't find any products matching your criteria. Try adjusting your search or browse our categories.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => {
                    setSearchQuery('')
                    setSelectedCategory('')
                  }}
                  className="bg-gradient-to-r from-orange-400 to-orange-500 text-white px-8 py-4 rounded-xl font-semibold hover:from-orange-500 hover:to-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Clear All Filters
                </button>
                <button
                  onClick={() => document.getElementById('categories')?.scrollIntoView({ behavior: 'smooth' })}
                  className="bg-white text-gray-700 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-200 border border-gray-200 shadow-md hover:shadow-lg"
                >
                  Browse Categories
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Why Choose Our Store */}
      <section className="bg-gradient-to-br from-orange-50 to-orange-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose Our Store?</h2>
            <p className="text-xl text-gray-600">Experience the best of Amazon shopping with our curated selection</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-400 to-green-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <span className="text-3xl">✅</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors">Trusted Products</h3>
              <p className="text-gray-600 text-lg leading-relaxed">All products are carefully selected from Amazon's vast catalog with verified reviews and ratings.</p>
            </div>
            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <span className="text-3xl">🚚</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">Fast Shipping</h3>
              <p className="text-gray-600 text-lg leading-relaxed">Enjoy Amazon's reliable and fast shipping options including Prime delivery for eligible items.</p>
            </div>
            <div className="text-center group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <span className="text-3xl">💰</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors">Best Deals</h3>
              <p className="text-gray-600 text-lg leading-relaxed">We curate the best deals and highest-rated products to ensure maximum value for your money.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Clean Footer */}
      <CleanFooter
        categories={categories}
        onCategorySelect={setSelectedCategory}
      />

      {/* Product Detail Modal */}
      <ProductDetailModal
        productId={selectedProductId}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  )
}
