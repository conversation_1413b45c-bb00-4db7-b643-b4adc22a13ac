import AmazonLogo from './AmazonLogo'

interface AmazonButtonProps {
  href: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary'
  children?: React.ReactNode
}

export default function AmazonButton({ 
  href, 
  className = '', 
  size = 'md', 
  variant = 'primary',
  children = 'Buy on Amazon'
}: AmazonButtonProps) {
  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  }

  const variantClasses = {
    primary: 'bg-gradient-to-b from-yellow-300 via-yellow-400 to-yellow-500 hover:from-yellow-400 hover:via-yellow-500 hover:to-yellow-600 text-gray-900 shadow-lg hover:shadow-xl border border-yellow-600',
    secondary: 'bg-white border-2 border-yellow-400 text-gray-900 hover:bg-yellow-50 shadow-md hover:shadow-lg'
  }

  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className={`
        inline-flex items-center justify-center gap-2
        font-bold rounded-lg transition-all duration-200
        transform hover:scale-105 active:scale-95
        focus:outline-none focus:ring-4 focus:ring-yellow-300
        relative overflow-hidden
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
    >
      {/* Amazon-style shine effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-20 transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"></div>

      <AmazonLogo size={size === 'lg' ? 'md' : 'sm'} />
      <span className="relative z-10 tracking-wide">
        {children}
      </span>

      {/* Amazon-style arrow */}
      <svg
        className="w-4 h-4 ml-1 transform transition-transform group-hover:translate-x-1"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    </a>
  )
}
