interface AmazonLogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export default function AmazonLogo({ className = '', size = 'md' }: AmazonLogoProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <svg
      className={`${sizeClasses[size]} ${className}`}
      viewBox="0 0 100 30"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Amazon logo path */}
      <path
        d="M28.1 20.8c-1.2.9-2.9 1.4-4.4 1.4-2.1 0-3.9-.8-5.1-2.2-1.2-1.4-1.8-3.2-1.8-5.4s.6-4 1.8-5.4c1.2-1.4 3-2.2 5.1-2.2 1.5 0 3.2.5 4.4 1.4v-1h2.8v13.4h-2.8v-1zm-7.5-6.2c0 1.4.3 2.5.9 3.3.6.8 1.5 1.2 2.6 1.2s2-.4 2.6-1.2c.6-.8.9-1.9.9-3.3s-.3-2.5-.9-3.3c-.6-.8-1.5-1.2-2.6-1.2s-2 .4-2.6 1.2c-.6.8-.9 1.9-.9 3.3z"
        fill="currentColor"
      />
      <path
        d="M44.8 7.4c1.8 0 3.2.6 4.2 1.8 1 1.2 1.5 2.8 1.5 4.8v8.4h-2.8v-1.2c-.9 1-2.2 1.5-3.8 1.5-1.4 0-2.6-.4-3.5-1.2-.9-.8-1.4-1.9-1.4-3.2 0-1.3.5-2.4 1.4-3.2.9-.8 2.1-1.2 3.5-1.2 1.3 0 2.5.3 3.6.9v-.6c0-.9-.3-1.6-.8-2.1-.5-.5-1.3-.8-2.2-.8-1.2 0-2.2.5-3 1.5l-2.2-1.8c1.3-1.6 3.1-2.4 5.5-2.4zm1.4 10.8v-1.4c-.8-.5-1.7-.7-2.7-.7-.7 0-1.3.2-1.7.6-.4.4-.6.9-.6 1.5s.2 1.1.6 1.5c.4.4 1 .6 1.7.6 1 0 1.9-.2 2.7-.7z"
        fill="currentColor"
      />
      <path
        d="M65.2 7.4c1.5 0 2.7.5 3.6 1.5.9 1 1.4 2.4 1.4 4.2v9.3h-2.8v-8.8c0-1.1-.3-1.9-.8-2.5-.5-.6-1.2-.9-2.1-.9-.9 0-1.6.3-2.1.9-.5.6-.8 1.4-.8 2.5v8.8h-2.8v-8.8c0-1.1-.3-1.9-.8-2.5-.5-.6-1.2-.9-2.1-.9-.9 0-1.6.3-2.1.9-.5.6-.8 1.4-.8 2.5v8.8h-2.8V7.8h2.8v1.2c.8-1 1.9-1.5 3.3-1.5 1.5 0 2.7.6 3.6 1.7.9-1.1 2.2-1.7 3.9-1.7z"
        fill="currentColor"
      />
      <path
        d="M82.1 7.4c1.8 0 3.2.6 4.2 1.8 1 1.2 1.5 2.8 1.5 4.8v8.4h-2.8v-1.2c-.9 1-2.2 1.5-3.8 1.5-1.4 0-2.6-.4-3.5-1.2-.9-.8-1.4-1.9-1.4-3.2 0-1.3.5-2.4 1.4-3.2.9-.8 2.1-1.2 3.5-1.2 1.3 0 2.5.3 3.6.9v-.6c0-.9-.3-1.6-.8-2.1-.5-.5-1.3-.8-2.2-.8-1.2 0-2.2.5-3 1.5l-2.2-1.8c1.3-1.6 3.1-2.4 5.5-2.4zm1.4 10.8v-1.4c-.8-.5-1.7-.7-2.7-.7-.7 0-1.3.2-1.7.6-.4.4-.6.9-.6 1.5s.2 1.1.6 1.5c.4.4 1 .6 1.7.6 1 0 1.9-.2 2.7-.7z"
        fill="currentColor"
      />
      {/* Amazon smile */}
      <path
        d="M73.2 26.8c-8.1 6-19.8 9.2-29.9 9.2-14.1 0-26.8-5.2-36.4-13.9-.8-.7-.1-1.6.8-1.1 10.6 6.2 23.7 9.9 37.2 9.9 9.1 0 19.1-1.9 28.3-5.8 1.4-.6 2.6.9 1.2 1.7z"
        fill="#FF9900"
      />
      <path
        d="M76.5 23c-1-.3-6.8-.3-9.5-.2-2.7.2-5.5.6-5.5.6s.6-.4 1.7-.8c5.1-1.8 13.5-1.3 14.5-.1 1 1.2-.3 9.5-5 13.5 0 0 .4-.2 1.2-.7 3.8-2.7 5.1-8.7 2.6-12.3z"
        fill="#FF9900"
      />
    </svg>
  )
}
