'use client'

import { useState } from 'react'

interface AmazonProductData {
  title: string
  price: string | null
  image: string | null
  description: string | null
  rating: string | null
  availability: string | null
  features: string[]
  productId: string | null
  originalUrl: string
}

interface AmazonUrlImporterProps {
  onDataImported: (data: AmazonProductData) => void
  onMultipleProductsFound?: (products: AmazonProductData[]) => void
  className?: string
}

export default function AmazonUrlImporter({ onDataImported, onMultipleProductsFound, className = '' }: AmazonUrlImporterProps) {
  const [amazonUrl, setAmazonUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const handleImport = async () => {
    if (!amazonUrl.trim()) {
      setError('Please enter an Amazon URL')
      return
    }

    setLoading(true)
    setError('')
    setSuccess(false)

    try {
      const response = await fetch('/api/scrape-amazon', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amazonUrl: amazonUrl.trim() }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        if (data.isMultiProduct && data.products && data.products.length > 1) {
          // Handle multiple products
          if (onMultipleProductsFound) {
            onMultipleProductsFound(data.products)
            setSuccess(true)
            setAmazonUrl('')
            setTimeout(() => setSuccess(false), 3000)
          } else {
            // Fallback: use first product
            onDataImported(data.products[0])
            setSuccess(true)
            setAmazonUrl('')
            setTimeout(() => setSuccess(false), 3000)
          }
        } else {
          // Single product
          onDataImported(data.data)
          setSuccess(true)
          setAmazonUrl('')
          setTimeout(() => setSuccess(false), 3000)
        }
      } else {
        setError(data.error || 'Failed to import product data')
      }
    } catch (error) {
      setError('Network error. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleImport()
    }
  }

  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center mb-3">
        <span className="text-2xl mr-2">🚀</span>
        <h3 className="text-lg font-semibold text-gray-900">Import from Amazon</h3>
      </div>
      
      <p className="text-sm text-gray-600 mb-4">
        Paste an Amazon product URL to automatically fill in the product details.
        For search results or category pages, you'll get a preview to select multiple products.
      </p>

      <div className="space-y-3">
        <div className="flex gap-2">
          <input
            type="url"
            value={amazonUrl}
            onChange={(e) => setAmazonUrl(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="https://amazon.com/dp/B08N5WRWNW"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            disabled={loading}
          />
          <button
            onClick={handleImport}
            disabled={loading || !amazonUrl.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium transition-colors"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Importing...
              </div>
            ) : (
              'Import'
            )}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md text-sm">
            <div className="flex items-center">
              <span className="mr-2">⚠️</span>
              {error}
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-3 py-2 rounded-md text-sm">
            <div className="flex items-center">
              <span className="mr-2">✅</span>
              Product data imported successfully! Form fields have been populated.
            </div>
          </div>
        )}

        <div className="text-xs text-gray-500">
          <p><strong>Supported:</strong> Amazon.com, Amazon.co.uk, Amazon.de, and other Amazon domains</p>
          <p><strong>Note:</strong> Some products may be protected from scraping. Manual entry may be required.</p>
        </div>
      </div>
    </div>
  )
}
