'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface Banner {
  id: string
  title: string
  image_url: string
  link_url: string | null
  is_active: boolean
  display_order: number
  start_date: string
  end_date: string | null
}

interface BannerCarouselProps {
  className?: string
}

export default function BannerCarousel({ className = '' }: BannerCarouselProps) {
  const [banners, setBanners] = useState<Banner[]>([])
  const [validBanners, setValidBanners] = useState<Banner[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())
  const [imageValidation, setImageValidation] = useState<Set<string>>(new Set())

  useEffect(() => {
    console.log('🎨 BannerCarousel component mounted')
    fetchBanners()
  }, [])

  useEffect(() => {
    if (validBanners.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % validBanners.length)
      }, 5000) // Auto-advance every 5 seconds

      return () => clearInterval(interval)
    }
  }, [validBanners.length])

  const validateImageUrl = async (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new window.Image()
      img.onload = () => resolve(true)
      img.onerror = () => resolve(false)
      img.src = url
      // Timeout after 5 seconds
      setTimeout(() => resolve(false), 5000)
    })
  }

  const fetchBanners = async () => {
    try {
      const response = await fetch('/api/banners')
      if (response.ok) {
        const data = await response.json()
        console.log('🎨 Fetched banners:', data.banners)
        const fetchedBanners = data.banners || []
        setBanners(fetchedBanners)

        // Validate banner images
        const validatedBanners: Banner[] = []
        for (const banner of fetchedBanners) {
          if (banner.image_url) {
            const isValid = await validateImageUrl(banner.image_url)
            if (isValid) {
              validatedBanners.push(banner)
              setImageValidation(prev => new Set([...prev, banner.id]))
            } else {
              console.log('🎨 Invalid banner image:', banner.title, banner.image_url)
            }
          }
        }

        setValidBanners(validatedBanners)
        console.log('🎨 Valid banners after validation:', validatedBanners.length)
      } else {
        console.error('🎨 Failed to fetch banners - HTTP', response.status)
      }
    } catch (error) {
      console.error('🎨 Failed to fetch banners:', error)
    } finally {
      setLoading(false)
    }
  }

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % validBanners.length)
  }

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + validBanners.length) % validBanners.length)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  const handleImageError = (bannerId: string) => {
    console.log('🎨 Image error for banner:', bannerId)
    setImageErrors(prev => new Set([...prev, bannerId]))
  }

  if (loading) {
    console.log('🔄 BannerCarousel is loading...')
    return (
      <div className={`relative w-full h-64 md:h-96 bg-gray-200 rounded-2xl animate-pulse ${className}`}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-gray-400">Loading banners...</div>
        </div>
        <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
          LOADING
        </div>
      </div>
    )
  }

  // If no valid banners after validation, don't render anything
  if (validBanners.length === 0) {
    console.log('🚫 No valid banners found, hiding banner section')
    return null
  }

  const currentBanner = validBanners[currentIndex]
  console.log('🎨 BannerCarousel rendering with', validBanners.length, 'valid banners, current:', currentIndex)

  const BannerContent = () => {
    const hasImageError = imageErrors.has(currentBanner.id)
    
    return (
      <div className="relative w-full h-64 md:h-96 rounded-2xl overflow-hidden group">
        {!hasImageError ? (
          <div className="relative w-full h-full">
            <Image
              src={currentBanner.image_url}
              alt={currentBanner.title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-105"
              priority={currentIndex === 0}
              onError={() => handleImageError(currentBanner.id)}
              onLoad={() => console.log('🎨 Banner image loaded:', currentBanner.title)}
            />
          </div>
        ) : (
          <div className="w-full h-full bg-gradient-to-r from-orange-400 to-orange-500 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="text-6xl mb-4">🖼️</div>
              <div className="text-lg font-medium">Image not available</div>
            </div>
          </div>
        )}

        {/* Subtle overlay for better navigation visibility */}
        <div className="absolute inset-0 bg-black bg-opacity-5"></div>

        {/* Navigation arrows */}
        {validBanners.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-200"
              aria-label="Previous banner"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-200"
              aria-label="Next banner"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}

        {/* Dots indicator */}
        {validBanners.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {validBanners.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? 'bg-white'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Debug indicator */}
        <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
          BANNER: {currentIndex + 1}/{validBanners.length}
        </div>
      </div>
    )
  }

  // Wrap with link if banner has a link_url
  if (currentBanner.link_url) {
    return (
      <Link href={currentBanner.link_url} className={className}>
        <BannerContent />
      </Link>
    )
  }

  return (
    <div className={className}>
      <BannerContent />
    </div>
  )
}
