'use client'

import Link from 'next/link'

interface Category {
  id: string
  name: string
  slug: string
}

interface CleanFooterProps {
  categories: Category[]
  onCategorySelect: (categoryId: string) => void
}

export default function CleanFooter({ categories, onCategorySelect }: CleanFooterProps) {
  return (
    <footer className="bg-gradient-to-br from-gray-900 to-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-xl">AD</span>
              </div>
              <h3 className="text-3xl font-bold bg-gradient-to-r from-orange-400 to-orange-500 bg-clip-text text-transparent">
                Amazing Deals
              </h3>
            </div>
            <p className="text-gray-300 mb-6 text-lg leading-relaxed">
              Discover incredible deals and amazing products curated just for you. 
              We help you find the best products at unbeatable prices.
            </p>
            <p className="text-sm text-gray-400 bg-gray-800 p-4 rounded-lg border border-gray-700">
              <strong>Disclaimer:</strong> As an Amazon Associate, we earn from qualifying purchases.<br />
              Prices and availability subject to change without notice.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-orange-400">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/" className="text-gray-300 hover:text-orange-400 transition-colors flex items-center space-x-2">
                  <span>🏠</span><span>Home</span>
                </Link>
              </li>
              <li>
                <button 
                  onClick={() => onCategorySelect('')} 
                  className="text-gray-300 hover:text-orange-400 transition-colors flex items-center space-x-2"
                >
                  <span>🛍️</span><span>All Products</span>
                </button>
              </li>
              <li>
                <Link href="#" className="text-gray-300 hover:text-orange-400 transition-colors flex items-center space-x-2">
                  <span>📞</span><span>Contact Us</span>
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-300 hover:text-orange-400 transition-colors flex items-center space-x-2">
                  <span>ℹ️</span><span>About Us</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-orange-400">Categories</h4>
            <ul className="space-y-3">
              {categories.slice(0, 6).map((category) => (
                <li key={category.id}>
                  <button
                    onClick={() => onCategorySelect(category.id)}
                    className="text-gray-300 hover:text-orange-400 transition-colors flex items-center space-x-2"
                  >
                    <span>
                      {category.name === 'Electronics' && '📱'}
                      {category.name === 'Books' && '📚'}
                      {category.name === 'Home & Garden' && '🏠'}
                      {category.name === 'Sports & Outdoors' && '⚽'}
                      {category.name === 'Health & Beauty' && '💄'}
                      {category.name === 'Fashion' && '👕'}
                      {!['Electronics', 'Books', 'Home & Garden', 'Sports & Outdoors', 'Health & Beauty', 'Fashion'].includes(category.name) && '🛍️'}
                    </span>
                    <span>{category.name}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex flex-wrap items-center space-x-6 mb-4 md:mb-0 text-sm text-gray-400">
              <Link href="#" className="hover:text-orange-400 transition-colors">Privacy Policy</Link>
              <Link href="#" className="hover:text-orange-400 transition-colors">Terms of Service</Link>
              <Link href="#" className="hover:text-orange-400 transition-colors">Cookie Policy</Link>
              <Link href="/admin/login" className="hover:text-orange-400 transition-colors">Admin Login</Link>
            </div>
            <div className="text-gray-400 text-sm">
              &copy; 2024 Amazing Deals. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
