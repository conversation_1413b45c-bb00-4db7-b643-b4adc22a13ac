'use client'

import { useState } from 'react'
import Link from 'next/link'

interface Category {
  id: string
  name: string
  slug: string
}

interface CleanHeaderProps {
  categories: Category[]
  searchQuery: string
  onSearchChange: (query: string) => void
  onSearchSubmit: (e: React.FormEvent) => void
  onCategorySelect: (categoryId: string) => void
  selectedCategory: string
}

export default function CleanHeader({ 
  categories, 
  searchQuery, 
  onSearchChange, 
  onSearchSubmit,
  onCategorySelect,
  selectedCategory 
}: CleanHeaderProps) {
  const [showMobileMenu, setShowMobileMenu] = useState(false)

  return (
    <header className="bg-white shadow-lg border-b-4 border-orange-500">
      {/* Top Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-4">
            <div className="bg-gradient-to-r from-orange-400 to-orange-500 text-white px-4 py-2 rounded-lg font-bold text-2xl shadow-lg">
              AD
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Amazing Deals</h1>
              <p className="text-gray-600">Best deals, curated for you</p>
            </div>
          </Link>

          {/* Desktop Search */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <form onSubmit={onSearchSubmit} className="flex w-full">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                placeholder="Search for amazing deals..."
                className="flex-1 px-4 py-3 text-gray-900 bg-white border-2 border-gray-300 rounded-l-xl focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200"
              />
              <button
                type="submit"
                className="px-6 py-3 bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 text-white rounded-r-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </form>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className="md:hidden p-2 rounded-lg text-gray-600 hover:text-orange-500 hover:bg-orange-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden pb-4">
          <form onSubmit={onSearchSubmit} className="flex">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Search for amazing deals..."
              className="flex-1 px-4 py-3 text-gray-900 bg-white border-2 border-gray-300 rounded-l-xl focus:outline-none focus:border-orange-500"
            />
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-orange-400 to-orange-500 text-white rounded-r-xl"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </form>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-gradient-to-r from-orange-50 to-orange-100 border-t border-orange-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Desktop Categories */}
          <div className="hidden md:flex items-center py-4 space-x-8">
            <button
              onClick={() => onCategorySelect('')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                selectedCategory === '' 
                  ? 'bg-orange-500 text-white shadow-lg' 
                  : 'text-gray-700 hover:text-orange-600 hover:bg-white'
              }`}
            >
              All Products
            </button>
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => onCategorySelect(category.id)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  selectedCategory === category.id 
                    ? 'bg-orange-500 text-white shadow-lg' 
                    : 'text-gray-700 hover:text-orange-600 hover:bg-white'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Mobile Categories */}
          {showMobileMenu && (
            <div className="md:hidden py-4 space-y-2">
              <button
                onClick={() => {
                  onCategorySelect('')
                  setShowMobileMenu(false)
                }}
                className={`block w-full text-left px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                  selectedCategory === '' 
                    ? 'bg-orange-500 text-white' 
                    : 'text-gray-700 hover:text-orange-600 hover:bg-white'
                }`}
              >
                All Products
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => {
                    onCategorySelect(category.id)
                    setShowMobileMenu(false)
                  }}
                  className={`block w-full text-left px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                    selectedCategory === category.id 
                      ? 'bg-orange-500 text-white' 
                      : 'text-gray-700 hover:text-orange-600 hover:bg-white'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
