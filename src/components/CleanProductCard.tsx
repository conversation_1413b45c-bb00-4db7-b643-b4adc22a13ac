'use client'

import Image from 'next/image'
import AmazonButton from './AmazonButton'

interface Product {
  id: string
  name: string
  description: string | null
  amazonAffiliateLink: string
  imageUrl: string | null
  price: number | null
  mrp?: number | null
  discountPercentage?: number | null
  category: {
    id: string
    name: string
  }
}

interface CleanProductCardProps {
  product: Product
  onViewDetails: (productId: string) => void
}

export default function CleanProductCard({ product, onViewDetails }: CleanProductCardProps) {
  const calculateDiscount = () => {
    if (product.discountPercentage) {
      return product.discountPercentage
    }
    if (product.mrp && product.price && product.mrp > product.price) {
      return Math.round(((product.mrp - product.price) / product.mrp) * 100)
    }
    return null
  }

  const discount = calculateDiscount()

  return (
    <div className="group card-modern overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 relative">
      {/* Product Image */}
      <div className="relative overflow-hidden aspect-[4/3]">
        {product.imageUrl ? (
          <Image
            src={product.imageUrl}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
              const fallback = target.nextElementSibling as HTMLElement
              if (fallback) fallback.style.display = 'flex'
            }}
          />
        ) : null}
        <div className={`absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center ${product.imageUrl ? 'hidden' : 'flex'}`}>
          <div className="text-center">
            <span className="text-gray-400 text-2xl mb-2 block">📦</span>
            <span className="text-gray-500 text-xs">No Image Available</span>
          </div>
        </div>

        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
            {product.category.name}
          </span>
        </div>

        {/* Discount Badge */}
        {discount && discount > 0 && (
          <div className="absolute top-4 right-4">
            <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
              {discount}% OFF
            </span>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors line-clamp-2">
          {product.name}
        </h3>

        {product.description && (
          <p className="text-gray-600 mb-3 line-clamp-2 text-xs leading-relaxed">
            {product.description}
          </p>
        )}

        {/* Pricing */}
        {product.price && (
          <div className="mb-3">
            <div className="flex items-baseline space-x-1 mb-1">
              <span className="text-lg font-bold text-green-600">
                ₹{product.price.toLocaleString()}
              </span>
              {product.mrp && product.mrp > product.price && (
                <span className="text-xs text-gray-500 line-through">
                  ₹{product.mrp.toLocaleString()}
                </span>
              )}
            </div>
            {discount && discount > 0 && (
              <div className="text-xs text-green-600 font-medium">
                {discount}% off
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-1.5">
          <button
            onClick={() => onViewDetails(product.id)}
            className="w-full btn-secondary text-xs py-2 px-3 rounded-lg font-medium"
          >
            View Details
          </button>
          <AmazonButton
            href={product.amazonAffiliateLink}
            className="w-full text-xs"
            size="sm"
          >
            Buy on Amazon
          </AmazonButton>
        </div>
      </div>
    </div>
  )
}
