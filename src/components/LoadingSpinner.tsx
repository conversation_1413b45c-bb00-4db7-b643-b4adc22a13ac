'use client'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  color?: 'orange' | 'gray' | 'white'
  className?: string
}

export default function LoadingSpinner({ 
  size = 'md', 
  color = 'orange', 
  className = '' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  const colorClasses = {
    orange: 'text-orange-500',
    gray: 'text-gray-500',
    white: 'text-white'
  }

  return (
    <div className={`inline-block ${className}`}>
      <svg
        className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]}`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
    </div>
  )
}

// Loading skeleton component for cards
export function LoadingSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`card-modern overflow-hidden ${className}`}>
      <div className="loading-shimmer h-56 w-full"></div>
      <div className="p-6 space-y-4">
        <div className="loading-shimmer h-4 w-3/4 rounded"></div>
        <div className="loading-shimmer h-3 w-full rounded"></div>
        <div className="loading-shimmer h-3 w-2/3 rounded"></div>
        <div className="flex justify-between items-center">
          <div className="loading-shimmer h-6 w-20 rounded"></div>
          <div className="loading-shimmer h-10 w-32 rounded-xl"></div>
        </div>
      </div>
    </div>
  )
}

// Loading grid for product listings
export function LoadingGrid({ count = 6 }: { count?: number }) {
  return (
    <div className="grid-modern">
      {Array.from({ length: count }).map((_, index) => (
        <LoadingSkeleton key={index} />
      ))}
    </div>
  )
}
