'use client'

import { useState } from 'react'
import Image from 'next/image'
import { autoMapCategory } from '@/lib/categoryMapping'

interface AmazonProductData {
  title: string
  price: string | null
  mrp?: string | null
  discountPercentage?: number | null
  image: string | null
  description: string | null
  rating: string | null
  availability: string | null
  features: string[]
  asin?: string | null
  url?: string | null
  amazonAffiliateLink?: string
}

interface Category {
  id: string
  name: string
}

interface MultiProductImporterProps {
  products: AmazonProductData[]
  categories: Category[]
  onClose: () => void
  onImportComplete: () => void
}

interface ProductImportStatus {
  status: 'pending' | 'importing' | 'success' | 'error' | 'duplicate'
  message?: string
}

export default function MultiProductImporter({
  products,
  categories,
  onClose,
  onImportComplete
}: MultiProductImporterProps) {
  // Auto-map categories for all products
  const autoMappedCategories = products.reduce((acc, product, index) => {
    const suggestedCategoryId = autoMapCategory(product.title, product.description, categories)
    if (suggestedCategoryId) {
      acc[index] = suggestedCategoryId
    }
    return acc
  }, {} as Record<number, string>)

  const [selectedProducts, setSelectedProducts] = useState<Set<number>>(new Set(Array.from({ length: products.length }, (_, i) => i)))
  const [productCategories, setProductCategories] = useState<Record<number, string>>(autoMappedCategories)
  const [importing, setImporting] = useState(false)
  const [importStatus, setImportStatus] = useState<Record<number, ProductImportStatus>>({})

  const handleProductToggle = (index: number) => {
    const newSelected = new Set(selectedProducts)
    if (newSelected.has(index)) {
      newSelected.delete(index)
    } else {
      newSelected.add(index)
    }
    setSelectedProducts(newSelected)
  }

  const handleCategoryChange = (index: number, categoryId: string) => {
    setProductCategories(prev => ({
      ...prev,
      [index]: categoryId
    }))
  }

  const handleSelectAll = () => {
    if (selectedProducts.size === products.length) {
      setSelectedProducts(new Set())
    } else {
      setSelectedProducts(new Set(Array.from({ length: products.length }, (_, i) => i)))
    }
  }

  const handleImportSelected = async () => {
    setImporting(true)
    const selectedIndices = Array.from(selectedProducts)
    
    // Initialize status for all selected products
    const initialStatus: Record<number, ProductImportStatus> = {}
    selectedIndices.forEach(index => {
      initialStatus[index] = { status: 'pending' }
    })
    setImportStatus(initialStatus)

    let successCount = 0
    let errorCount = 0
    let duplicateCount = 0

    for (const index of selectedIndices) {
      const product = products[index]
      const categoryId = productCategories[index] || categories[0]?.id

      if (!categoryId) {
        setImportStatus(prev => ({
          ...prev,
          [index]: { status: 'error', message: 'No category selected' }
        }))
        errorCount++
        continue
      }

      // Update status to importing
      setImportStatus(prev => ({
        ...prev,
        [index]: { status: 'importing' }
      }))

      try {
        // Clean and validate product name
        const cleanName = product.title.replace(/[^\w\s\-\(\)\[\]\.]/g, '').trim()
        const finalName = cleanName.length > 500 ? cleanName.substring(0, 500) : cleanName

        // Extract price and calculate MRP/discount - use scraped data if available
        const currentPrice = product.price ? parseFloat(product.price.replace(/[^\d.]/g, '')) : null

        let estimatedMrp = null
        let discountPercentage = null

        if (currentPrice) {
          // Use scraped MRP if available, otherwise estimate
          if (product.mrp) {
            const cleanMrp = product.mrp.replace(/[^\d.]/g, '')
            if (cleanMrp && !isNaN(parseFloat(cleanMrp))) {
              estimatedMrp = parseFloat(cleanMrp)
            }
          }

          // Fallback: estimate MRP as 20% higher than current price
          if (!estimatedMrp) {
            estimatedMrp = Math.round(currentPrice * 1.2 * 100) / 100
          }

          // Use scraped discount if available, otherwise calculate
          if (product.discountPercentage !== null && product.discountPercentage !== undefined) {
            discountPercentage = product.discountPercentage
          } else if (estimatedMrp > currentPrice) {
            discountPercentage = Math.round(((estimatedMrp - currentPrice) / estimatedMrp) * 100)
          }
        }

        // Handle description - provide fallback or generate from title
        let description = product.description || product.features?.join('. ') || null
        if (!description && finalName) {
          // Generate basic description from product name
          description = `${finalName} - Available on Amazon with great deals and fast delivery.`
        }

        const productData = {
          name: finalName,
          description: description,
          amazonAffiliateLink: product.amazonAffiliateLink || product.url || '',
          imageUrl: product.image || '',
          price: currentPrice,
          mrp: estimatedMrp,
          discountPercentage: discountPercentage,
          categoryId: categoryId
        }

        const response = await fetch('/api/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(productData),
        })

        if (response.ok) {
          setImportStatus(prev => ({
            ...prev,
            [index]: { status: 'success', message: 'Product imported successfully' }
          }))
          successCount++
        } else {
          const errorData = await response.json()
          if (response.status === 409) {
            // Duplicate product
            setImportStatus(prev => ({
              ...prev,
              [index]: { status: 'duplicate', message: 'Product already exists in this category' }
            }))
            duplicateCount++
          } else {
            setImportStatus(prev => ({
              ...prev,
              [index]: { status: 'error', message: errorData.error || 'Failed to import product' }
            }))
            errorCount++
          }
        }
      } catch (error) {
        setImportStatus(prev => ({
          ...prev,
          [index]: { status: 'error', message: 'Network error' }
        }))
        errorCount++
      }

      // Small delay between imports to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    setImporting(false)

    // Send bulk import notification to Telegram if there were successful imports
    if (successCount > 0) {
      try {
        const successfulProducts = selectedIndices
          .filter((_, i) => i < successCount)
          .map(index => {
            const product = products[index]
            const currentPrice = product.price ? parseFloat(product.price.replace(/[^\d.]/g, '')) : null
            return {
              id: `bulk-${index}`,
              name: product.title,
              description: product.description || undefined,
              price: currentPrice || undefined,
              category: categories.find(c => c.id === productCategories[index])?.name || 'General',
              amazonAffiliateLink: product.amazonAffiliateLink || product.url || '',
              imageUrl: product.image || undefined
            }
          })

        await fetch('/api/telegram/bulk-import', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ products: successfulProducts })
        })
      } catch (error) {
        console.error('Failed to send bulk import notification:', error)
      }
    }

    // Show summary
    const totalProcessed = successCount + errorCount + duplicateCount
    alert(`Import completed!\n\nSuccessful: ${successCount}\nDuplicates: ${duplicateCount}\nErrors: ${errorCount}\nTotal: ${totalProcessed}`)

    if (successCount > 0) {
      onImportComplete()
    }
  }

  const getStatusIcon = (status: ProductImportStatus) => {
    switch (status.status) {
      case 'pending': return '⏳'
      case 'importing': return '🔄'
      case 'success': return '✅'
      case 'error': return '❌'
      case 'duplicate': return '⚠️'
      default: return ''
    }
  }

  const getStatusColor = (status: ProductImportStatus) => {
    switch (status.status) {
      case 'pending': return 'text-gray-500'
      case 'importing': return 'text-blue-500'
      case 'success': return 'text-green-500'
      case 'error': return 'text-red-500'
      case 'duplicate': return 'text-yellow-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Import Multiple Products</h2>
            <p className="text-gray-600">
              Found <span className="font-semibold text-orange-600">{products.length}</span> products from Amazon search results.
              Select which ones to import.
            </p>
            {products.length > 20 && (
              <p className="text-sm text-green-600 mt-1">
                ✨ Enhanced extraction found {products.length - 20} additional products beyond the standard 20!
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
          >
            ×
          </button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSelectAll}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              >
                {selectedProducts.size === products.length ? 'Deselect All' : 'Select All'}
              </button>
              <span className="text-gray-600">
                {selectedProducts.size} of {products.length} selected
              </span>
            </div>
            <button
              onClick={handleImportSelected}
              disabled={selectedProducts.size === 0 || importing}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {importing ? 'Importing...' : `Import Selected (${selectedProducts.size})`}
            </button>
          </div>
        </div>

        {/* Products List */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-4">
            {products.map((product, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 transition-all ${
                  selectedProducts.has(index) 
                    ? 'border-orange-300 bg-orange-50' 
                    : 'border-gray-200 bg-white'
                }`}
              >
                <div className="flex items-start space-x-4">
                  {/* Checkbox */}
                  <div className="flex items-center pt-2">
                    <input
                      type="checkbox"
                      checked={selectedProducts.has(index)}
                      onChange={() => handleProductToggle(index)}
                      className="w-5 h-5 text-orange-500 rounded focus:ring-orange-500"
                      disabled={importing}
                    />
                  </div>

                  {/* Product Image */}
                  <div className="w-20 h-20 flex-shrink-0">
                    {product.image ? (
                      <Image
                        src={product.image}
                        alt={product.title}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-gray-400 text-sm">📦</span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {product.title}
                    </h3>
                    {product.price && (
                      <p className="text-green-600 font-semibold mb-2">
                        ₹{parseFloat(product.price).toLocaleString()}
                      </p>
                    )}
                    {product.description && (
                      <p className="text-gray-600 text-sm line-clamp-2 mb-2">
                        {product.description}
                      </p>
                    )}
                  </div>

                  {/* Category Selection */}
                  <div className="w-48 flex-shrink-0">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      value={productCategories[index] || ''}
                      onChange={(e) => handleCategoryChange(index, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
                      disabled={importing}
                    >
                      <option value="">Select category</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Status */}
                  <div className="w-24 flex-shrink-0 text-center">
                    {importStatus[index] && (
                      <div className={`text-sm ${getStatusColor(importStatus[index])}`}>
                        <div className="text-lg mb-1">
                          {getStatusIcon(importStatus[index])}
                        </div>
                        <div className="text-xs">
                          {importStatus[index].status === 'importing' && 'Importing...'}
                          {importStatus[index].status === 'success' && 'Success'}
                          {importStatus[index].status === 'error' && 'Error'}
                          {importStatus[index].status === 'duplicate' && 'Duplicate'}
                          {importStatus[index].status === 'pending' && 'Pending'}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
