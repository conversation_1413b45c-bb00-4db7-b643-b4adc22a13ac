'use client'

import { useState, useEffect, useRef } from 'react'
import Image from 'next/image'
import AmazonButton from './AmazonButton'

interface Product {
  id: string
  name: string
  description: string | null
  amazonAffiliateLink: string
  imageUrl: string | null
  price: number | null
  category: {
    id: string
    name: string
  }
}

interface ProductCarouselProps {
  products: Product[]
  title?: string
  className?: string
  onViewDetails?: (productId: string) => void
}

export default function ProductCarousel({ 
  products, 
  title = "Featured Products", 
  className = '',
  onViewDetails 
}: ProductCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [visibleProducts, setVisibleProducts] = useState(4)
  const carouselRef = useRef<HTMLDivElement>(null)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)

  // Responsive visible products
  useEffect(() => {
    const updateVisibleProducts = () => {
      const width = window.innerWidth
      if (width < 640) setVisibleProducts(1)
      else if (width < 768) setVisibleProducts(2)
      else if (width < 1024) setVisibleProducts(3)
      else setVisibleProducts(4)
    }

    updateVisibleProducts()
    window.addEventListener('resize', updateVisibleProducts)
    return () => window.removeEventListener('resize', updateVisibleProducts)
  }, [])

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying && products.length > visibleProducts) {
      autoPlayRef.current = setInterval(() => {
        setCurrentIndex(prev => {
          const maxIndex = Math.max(0, products.length - visibleProducts)
          return prev >= maxIndex ? 0 : prev + 1
        })
      }, 4000)
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current)
      }
    }
  }, [isAutoPlaying, products.length, visibleProducts])

  const handlePrevious = () => {
    setCurrentIndex(prev => {
      const maxIndex = Math.max(0, products.length - visibleProducts)
      return prev <= 0 ? maxIndex : prev - 1
    })
  }

  const handleNext = () => {
    setCurrentIndex(prev => {
      const maxIndex = Math.max(0, products.length - visibleProducts)
      return prev >= maxIndex ? 0 : prev + 1
    })
  }

  const handleDotClick = (index: number) => {
    setCurrentIndex(index)
  }

  const handleMouseEnter = () => {
    setIsAutoPlaying(false)
  }

  const handleMouseLeave = () => {
    setIsAutoPlaying(true)
  }

  if (products.length === 0) {
    return null
  }

  const maxIndex = Math.max(0, products.length - visibleProducts)
  const totalDots = maxIndex + 1

  return (
    <div className={`bg-white rounded-2xl shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrevious}
              className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-all duration-200 hover:bg-orange-50"
              disabled={products.length <= visibleProducts}
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={handleNext}
              className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-all duration-200 hover:bg-orange-50"
              disabled={products.length <= visibleProducts}
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Carousel */}
      <div 
        className="relative overflow-hidden"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div 
          ref={carouselRef}
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${currentIndex * (100 / visibleProducts)}%)`,
            width: `${(products.length / visibleProducts) * 100}%`
          }}
        >
          {products.map((product) => (
            <div 
              key={product.id} 
              className="flex-shrink-0 p-4"
              style={{ width: `${100 / products.length}%` }}
            >
              <div className="bg-white rounded-xl border border-gray-200 hover:border-orange-300 hover:shadow-lg transition-all duration-300 h-full flex flex-col">
                {/* Product Image */}
                <div className="relative overflow-hidden rounded-t-xl">
                  {product.imageUrl ? (
                    <Image
                      src={product.imageUrl}
                      alt={product.name}
                      width={300}
                      height={200}
                      className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const fallback = target.nextElementSibling as HTMLElement
                        if (fallback) fallback.style.display = 'flex'
                      }}
                    />
                  ) : null}
                  <div className={`w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center ${product.imageUrl ? 'hidden' : 'flex'}`}>
                    <div className="text-center">
                      <span className="text-gray-400 text-3xl mb-2 block">📦</span>
                      <span className="text-gray-500 text-sm">No Image</span>
                    </div>
                  </div>
                  
                  {/* Category Badge */}
                  <div className="absolute top-2 left-2">
                    <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                      {product.category.name}
                    </span>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4 flex-1 flex flex-col">
                  <h3 className="font-semibold text-gray-900 text-sm mb-2 line-clamp-2 flex-1">
                    {product.name}
                  </h3>
                  
                  {product.price && (
                    <div className="text-lg font-bold text-green-600 mb-3">
                      ₹{product.price.toLocaleString()}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    {onViewDetails && (
                      <button 
                        onClick={() => onViewDetails(product.id)}
                        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-3 rounded-lg transition-colors text-sm"
                      >
                        View Details
                      </button>
                    )}
                    <AmazonButton
                      href={product.amazonAffiliateLink}
                      className="w-full text-sm"
                      size="sm"
                    >
                      Buy Now
                    </AmazonButton>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Dots Indicator */}
      {totalDots > 1 && (
        <div className="flex justify-center py-4 space-x-2">
          {Array.from({ length: totalDots }).map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                index === currentIndex 
                  ? 'bg-orange-500 w-6' 
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      )}

      {/* Auto-play indicator */}
      <div className="absolute top-4 right-4">
        <div className={`w-2 h-2 rounded-full ${isAutoPlaying ? 'bg-green-400' : 'bg-gray-400'}`} />
      </div>
    </div>
  )
}
