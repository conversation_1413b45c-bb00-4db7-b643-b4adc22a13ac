'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import AmazonButton from './AmazonButton'

interface Product {
  id: string
  name: string
  description: string | null
  amazonAffiliateLink: string
  imageUrl: string | null
  price: number | null
  category: {
    id: string
    name: string
  }
  createdBy: {
    name: string
  }
}

interface ProductDetailModalProps {
  productId: string | null
  isOpen: boolean
  onClose: () => void
}

export default function ProductDetailModal({ productId, isOpen, onClose }: ProductDetailModalProps) {
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen && productId) {
      fetchProduct()
    }
  }, [isOpen, productId])

  const fetchProduct = async () => {
    if (!productId) return
    
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch(`/api/products/${productId}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data.product)
      } else {
        setError('Failed to load product details')
      }
    } catch (error) {
      setError('Failed to load product details')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Product Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading && (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <div className="text-red-500 text-lg mb-4">⚠️ {error}</div>
              <button
                onClick={fetchProduct}
                className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {product && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Product Image */}
              <div className="space-y-4">
                {product.imageUrl ? (
                  <Image
                    src={product.imageUrl}
                    alt={product.name}
                    width={500}
                    height={400}
                    className="w-full h-80 object-cover rounded-xl"
                  />
                ) : (
                  <div className="w-full h-80 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center">
                    <div className="text-center">
                      <span className="text-gray-400 text-4xl mb-2 block">📦</span>
                      <span className="text-gray-500">No Image Available</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="space-y-6">
                <div>
                  <span className="inline-block px-3 py-1 bg-orange-100 text-orange-600 rounded-full text-sm font-semibold mb-3">
                    {product.category.name}
                  </span>
                  <h1 className="text-3xl font-bold text-gray-900 mb-4">
                    {product.name}
                  </h1>
                  {product.price && (
                    <div className="text-3xl font-bold text-green-600 mb-4">
                      ₹{product.price.toLocaleString()}
                    </div>
                  )}
                </div>

                {product.description && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {product.description}
                    </p>
                  </div>
                )}

                <div className="space-y-3">
                  <AmazonButton
                    href={product.amazonAffiliateLink}
                    className="w-full"
                    size="lg"
                  >
                    Buy on Amazon
                  </AmazonButton>
                  
                  <button
                    onClick={onClose}
                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-200"
                  >
                    Close
                  </button>
                </div>

                <div className="text-sm text-gray-500 pt-4 border-t border-gray-200">
                  <p>Added by {product.createdBy.name}</p>
                  <p className="mt-1">
                    <strong>Disclaimer:</strong> As an Amazon Associate, we earn from qualifying purchases.
                    Prices and availability subject to change.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
