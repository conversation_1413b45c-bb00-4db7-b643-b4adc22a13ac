import * as cheerio from 'cheerio'

export interface AmazonProductData {
  title: string
  price: string | null
  mrp: string | null
  discountPercentage: number | null
  image: string | null
  description: string | null
  rating: string | null
  availability: string | null
  features: string[]
  asin?: string | null
  url?: string | null
}

export interface AmazonScrapingResult {
  products: AmazonProductData[]
  isMultiProduct: boolean
  totalFound: number
}

export class AmazonScraper {
  private static readonly USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  ]

  private static getRandomUserAgent(): string {
    return this.USER_AGENTS[Math.floor(Math.random() * this.USER_AGENTS.length)]
  }

  private static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  static async scrapeProducts(amazonUrl: string): Promise<AmazonScrapingResult | null> {
    try {
      // Add random delay to avoid being blocked
      await this.delay(Math.random() * 3000 + 2000)

      // Try multiple approaches
      const approaches = [
        () => this.scrapeWithFetch(amazonUrl),
        () => this.scrapeWithAlternativeMethod(amazonUrl)
      ]

      for (const approach of approaches) {
        try {
          const html = await approach()
          if (html) {
            const result = this.parseMultipleProducts(html, amazonUrl)
            if (result && result.products.length > 0) {
              return result
            }
          }
        } catch (error) {
          console.log('Approach failed, trying next...', error)
          continue
        }
      }

      return null
    } catch (error) {
      console.error('Error scraping Amazon products:', error)
      return null
    }
  }

  // Legacy method for backward compatibility
  static async scrapeProduct(amazonUrl: string): Promise<AmazonProductData | null> {
    const result = await this.scrapeProducts(amazonUrl)
    return result && result.products.length > 0 ? result.products[0] : null
  }

  private static async scrapeWithFetch(amazonUrl: string): Promise<string | null> {
    const response = await fetch(amazonUrl, {
      headers: {
        'User-Agent': this.getRandomUserAgent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.text()
  }

  private static async scrapeWithAlternativeMethod(amazonUrl: string): Promise<string | null> {
    // Alternative approach using different headers and URL modifications
    const modifiedUrl = amazonUrl.includes('?')
      ? `${amazonUrl}&ref=sr_1_1`
      : `${amazonUrl}?ref=sr_1_1`

    const response = await fetch(modifiedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
        'Accept': '*/*',
        'Accept-Language': 'en',
        'Cache-Control': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`Alternative method failed: ${response.status}`)
    }

    return await response.text()
  }

  private static parseMultipleProducts(html: string, originalUrl: string): AmazonScrapingResult | null {
    const $ = cheerio.load(html)
    const products: AmazonProductData[] = []

    // Check if this is a single product page or a listing page
    const isSingleProduct = this.isSingleProductPage($, originalUrl)

    if (isSingleProduct) {
      // Parse single product
      const productData = this.parseProductData(html)
      if (productData) {
        products.push(productData)
      }
    } else {
      // Parse multiple products from listing page
      const listingProducts = this.parseListingProducts($, originalUrl)
      products.push(...listingProducts)
    }

    return {
      products,
      isMultiProduct: !isSingleProduct,
      totalFound: products.length
    }
  }

  private static parseProductData(html: string): AmazonProductData | null {
    const $ = cheerio.load(html)

    // Extract product data using various selectors (Amazon changes these frequently)
    const title = this.extractTitle($)
    const price = this.extractPrice($)
    const mrp = this.extractMRP($)
    const discountPercentage = this.calculateDiscount(price, mrp)

    const productData: AmazonProductData = {
      title,
      price,
      mrp,
      discountPercentage,
      image: this.extractImage($),
      description: this.extractDescription($),
      rating: this.extractRating($),
      availability: this.extractAvailability($),
      features: this.extractFeatures($)
    }

    // Validate that we got at least a title
    if (!productData.title || productData.title === 'Product Title Not Found') {
      return null
    }

    return productData
  }

  private static isSingleProductPage($: cheerio.CheerioAPI, url: string): boolean {
    // Check URL patterns for single product pages
    const singleProductPatterns = [
      /\/dp\/[A-Z0-9]{10}/,
      /\/gp\/product\/[A-Z0-9]{10}/,
      /\/product\/[A-Z0-9]{10}/
    ]

    const isSingleProductUrl = singleProductPatterns.some(pattern => pattern.test(url))

    // Also check for single product page elements
    const hasSingleProductElements = $('#productTitle').length > 0 ||
                                   $('.product-title').length > 0 ||
                                   $('[data-automation-id="product-title"]').length > 0

    return isSingleProductUrl && hasSingleProductElements
  }

  private static parseListingProducts($: cheerio.CheerioAPI, baseUrl: string): AmazonProductData[] {
    const products: AmazonProductData[] = []
    const seenAsins = new Set<string>() // Prevent duplicates

    // Enhanced selectors for product listings - more comprehensive
    const productSelectors = [
      '[data-component-type="s-search-result"]',
      '.s-result-item[data-asin]',
      '.sg-col-inner .s-widget-container[data-asin]',
      '.a-section.a-spacing-base[data-asin]',
      '[data-asin]:not([data-asin=""])',
      '.s-card-container[data-asin]',
      '.s-widget[data-asin]',
      '.s-item-container[data-asin]'
    ]

    console.log('Starting product extraction from Amazon search results...')

    for (const selector of productSelectors) {
      const productElements = $(selector)
      console.log(`Trying selector "${selector}" - found ${productElements.length} elements`)

      if (productElements.length > 0) {
        productElements.each((_, element) => {
          const $element = $(element)
          const asin = $element.attr('data-asin')

          // Skip if we've already processed this ASIN
          if (asin && seenAsins.has(asin)) {
            return
          }

          const product = this.extractListingProduct($element, baseUrl)
          if (product && product.title && product.title !== 'Product Title Not Found') {
            if (asin) seenAsins.add(asin)
            products.push(product)
          }
        })

        // If we found products with this selector, break to avoid duplicates
        if (products.length > 0) break
      }
    }

    // Also try to extract products from any remaining elements that might have been missed
    if (products.length < 10) {
      console.log('Low product count, trying additional extraction methods...')

      // Try more generic selectors
      const fallbackSelectors = [
        '.s-result-item',
        '.sg-col-inner',
        '[data-cy="title-recipe-title"]',
        '.a-section:has(.a-size-base-plus)',
        '.a-section:has(.a-price)'
      ]

      for (const selector of fallbackSelectors) {
        const elements = $(selector)
        elements.each((_, element) => {
          const $element = $(element)
          const product = this.extractListingProduct($element, baseUrl)
          if (product && product.title && product.title !== 'Product Title Not Found') {
            // Check for duplicates by title
            const isDuplicate = products.some(p => p.title === product.title)
            if (!isDuplicate) {
              products.push(product)
            }
          }
        })

        if (products.length >= 50) break // Reasonable limit
      }
    }

    console.log(`Total products extracted: ${products.length}`)

    // Return all products found (removed the 20 product limit)
    return products.filter(p => p.title && p.title.length > 3)
  }

  private static extractListingProduct($element: cheerio.Cheerio<any>, baseUrl: string): AmazonProductData | null {
    try {
      // Extract ASIN
      const asin = $element.attr('data-asin') ||
                   $element.find('[data-asin]').first().attr('data-asin')

      // Extract title with enhanced selectors
      const titleSelectors = [
        'h2 a span',
        'h2 span',
        '.s-size-mini span',
        '[data-cy="title-recipe-title"]',
        '.a-size-base-plus',
        '.a-size-medium',
        '.a-size-base',
        '.s-link-style a span',
        '.s-color-base',
        'a[href*="/dp/"] span',
        '.a-link-normal span',
        '.a-text-normal'
      ]

      let title = ''
      for (const selector of titleSelectors) {
        const titleElement = $element.find(selector).first()
        title = titleElement.text().trim()

        // Clean up title
        if (title && title.length > 3) {
          // Remove common unwanted text
          title = title.replace(/^(Sponsored|Ad)\s*/i, '')
          title = title.replace(/\s+/g, ' ')
          break
        }
      }

      // Fallback: try to get title from any link text
      if (!title || title.length <= 3) {
        const linkText = $element.find('a').first().text().trim()
        if (linkText && linkText.length > 3) {
          title = linkText.replace(/^(Sponsored|Ad)\s*/i, '').replace(/\s+/g, ' ')
        }
      }

      // Extract price
      const priceSelectors = [
        '.a-price-whole',
        '.a-price .a-offscreen',
        '.a-price-symbol',
        '.a-price-range'
      ]

      let price = null
      for (const selector of priceSelectors) {
        const priceText = $element.find(selector).first().text().trim()
        if (priceText) {
          price = this.cleanPrice(priceText)
          break
        }
      }

      // Extract MRP (for listings, it's usually not available, so we'll estimate)
      let mrp = null
      const mrpSelectors = [
        '.a-text-strike',
        '.a-price-was .a-offscreen'
      ]

      for (const selector of mrpSelectors) {
        const mrpText = $element.find(selector).first().text().trim()
        if (mrpText) {
          mrp = this.cleanPrice(mrpText)
          break
        }
      }

      // If no MRP found, estimate it as 20% higher than current price
      if (!mrp && price) {
        const currentPrice = parseFloat(price.replace(/[^\d.]/g, ''))
        if (!isNaN(currentPrice)) {
          mrp = (currentPrice * 1.2).toFixed(2)
        }
      }

      // Calculate discount percentage
      const discountPercentage = this.calculateDiscount(price, mrp)

      // Extract image
      const imageSelectors = [
        '.s-image',
        '.a-dynamic-image',
        'img[data-src]',
        'img[src]'
      ]

      let image = null
      for (const selector of imageSelectors) {
        const img = $element.find(selector).first()
        let src = img.attr('src') || img.attr('data-src')
        if (src) {
          if (src.startsWith('//')) src = 'https:' + src
          if (src.startsWith('/')) src = 'https://amazon.com' + src
          image = src
          break
        }
      }

      // Extract rating
      const ratingSelectors = [
        '.a-icon-alt',
        '[aria-label*="stars"]',
        '.a-star-medium .a-icon-alt'
      ]

      let rating = null
      for (const selector of ratingSelectors) {
        const ratingText = $element.find(selector).first().attr('aria-label') ||
                          $element.find(selector).first().text()
        if (ratingText && ratingText.includes('star')) {
          rating = ratingText.split(' ')[0]
          break
        }
      }

      // Create product URL if we have ASIN
      let productUrl = null
      if (asin) {
        const urlObj = new URL(baseUrl)
        productUrl = `https://${urlObj.hostname}/dp/${asin}`
      }

      if (!title || title.length < 3) return null

      return {
        title,
        price,
        mrp,
        discountPercentage,
        image,
        description: null, // Not available in listings
        rating,
        availability: null, // Not available in listings
        features: [], // Not available in listings
        asin,
        url: productUrl
      }
    } catch (error) {
      console.error('Error extracting listing product:', error)
      return null
    }
  }

  private static extractTitle($: cheerio.CheerioAPI): string {
    const selectors = [
      '#productTitle',
      '.product-title',
      '[data-automation-id="product-title"]',
      'h1.a-size-large',
      'h1 span'
    ]

    for (const selector of selectors) {
      const title = $(selector).first().text().trim()
      if (title) return title
    }

    return 'Product Title Not Found'
  }

  private static extractPrice($: cheerio.CheerioAPI): string | null {
    // Enhanced selectors for current price (selling price)
    const selectors = [
      '.a-price.a-text-price.a-size-medium.apexPriceToPay .a-offscreen',
      '.a-price.a-text-price.a-size-medium .a-offscreen',
      '.a-price .a-offscreen',
      '.a-price-whole',
      '#priceblock_dealprice',
      '#priceblock_ourprice',
      '.a-price-range .a-offscreen',
      '.a-price.a-text-price .a-offscreen',
      '[data-a-price-amount]',
      '#apex_desktop .a-price .a-offscreen',
      '.a-price-symbol',
      '.a-price-fraction'
    ]

    for (const selector of selectors) {
      const priceElement = $(selector).first()
      let priceText = priceElement.text().trim()

      // Also check data attributes
      if (!priceText) {
        priceText = priceElement.attr('data-a-price-amount') || ''
      }

      if (priceText) {
        const cleanedPrice = this.cleanPrice(priceText)
        if (cleanedPrice) {
          console.log(`🔍 Found price with selector "${selector}": ${priceText} -> ${cleanedPrice}`)
          return cleanedPrice
        }
      }
    }

    // Try to find price in combination (whole + fraction)
    const whole = $('.a-price-whole').first().text().trim()
    const fraction = $('.a-price-fraction').first().text().trim()

    if (whole) {
      const combinedPrice = fraction ? `${whole}.${fraction}` : whole
      const cleanedPrice = this.cleanPrice(combinedPrice)
      if (cleanedPrice) {
        console.log(`🔍 Found combined price: ${combinedPrice} -> ${cleanedPrice}`)
        return cleanedPrice
      }
    }

    console.log('⚠️ No price found with any selector')
    return null
  }

  private static extractMRP($: cheerio.CheerioAPI): string | null {
    // Enhanced selectors for MRP (original/list price)
    const selectors = [
      '.a-price.a-text-price.a-size-base .a-offscreen',
      '.a-text-strike .a-offscreen',
      '.a-text-strike',
      '#priceblock_listprice',
      '.a-price-was .a-offscreen',
      '.a-price.a-text-price.a-size-base',
      '[data-a-strike="true"] .a-offscreen',
      '[data-a-strike="true"]',
      '.a-price-was',
      '.a-text-price.a-size-base.a-color-secondary .a-offscreen',
      '.a-text-price.a-size-base .a-offscreen',
      '.a-price.a-text-price.a-size-base.a-color-secondary .a-offscreen'
    ]

    for (const selector of selectors) {
      const mrpElement = $(selector).first()
      let mrpText = mrpElement.text().trim()

      // Also check data attributes
      if (!mrpText) {
        mrpText = mrpElement.attr('data-a-price-amount') || ''
      }

      if (mrpText) {
        const cleanedMRP = this.cleanPrice(mrpText)
        if (cleanedMRP) {
          console.log(`🔍 Found MRP with selector "${selector}": ${mrpText} -> ${cleanedMRP}`)
          return cleanedMRP
        }
      }
    }

    console.log('⚠️ No MRP found with any selector')
    return null
  }

  private static calculateDiscount(currentPrice: string | null, mrp: string | null): number | null {
    if (!currentPrice || !mrp) {
      console.log('⚠️ Missing price data for discount calculation:', { currentPrice, mrp })
      return null
    }

    const current = parseFloat(currentPrice.replace(/[^\d.]/g, ''))
    const maximum = parseFloat(mrp.replace(/[^\d.]/g, ''))

    console.log(`🧮 Discount calculation: Current=${current}, MRP=${maximum}`)

    if (isNaN(current) || isNaN(maximum)) {
      console.log('⚠️ Invalid price values for discount calculation')
      return null
    }

    if (maximum <= current) {
      console.log('⚠️ MRP is not greater than current price, no discount')
      return null
    }

    const discount = Math.round(((maximum - current) / maximum) * 100)
    console.log(`✅ Calculated discount: ${discount}%`)

    return discount
  }

  private static extractImage($: cheerio.CheerioAPI): string | null {
    const selectors = [
      '#landingImage',
      '.a-dynamic-image',
      '#imgBlkFront',
      '.a-image-wrapper img',
      '[data-automation-id="product-image"] img'
    ]

    for (const selector of selectors) {
      const img = $(selector).first()
      let src = img.attr('src') || img.attr('data-src') || img.attr('data-old-hires')
      
      if (src) {
        // Get high resolution image if available
        const dataSrc = img.attr('data-a-dynamic-image')
        if (dataSrc) {
          try {
            const imageData = JSON.parse(dataSrc)
            const images = Object.keys(imageData)
            if (images.length > 0) {
              src = images[0] // Get the first (usually highest resolution) image
            }
          } catch (e) {
            // Continue with original src
          }
        }
        
        // Ensure it's a full URL
        if (src.startsWith('//')) {
          src = 'https:' + src
        } else if (src.startsWith('/')) {
          src = 'https://amazon.com' + src
        }
        
        return src
      }
    }

    return null
  }

  private static extractDescription($: cheerio.CheerioAPI): string | null {
    const selectors = [
      '#feature-bullets ul',
      '.a-unordered-list.a-vertical.a-spacing-mini',
      '[data-automation-id="product-overview"]',
      '#productDescription p'
    ]

    for (const selector of selectors) {
      const desc = $(selector).first().text().trim()
      if (desc && desc.length > 20) {
        return desc.substring(0, 500) + (desc.length > 500 ? '...' : '')
      }
    }

    return null
  }

  private static extractRating($: cheerio.CheerioAPI): string | null {
    const selectors = [
      '.a-icon-alt',
      '[data-hook="average-star-rating"] .a-icon-alt',
      '.a-star-medium .a-icon-alt'
    ]

    for (const selector of selectors) {
      const rating = $(selector).first().text().trim()
      if (rating && rating.includes('out of')) {
        return rating.split(' ')[0]
      }
    }

    return null
  }

  private static extractAvailability($: cheerio.CheerioAPI): string | null {
    const selectors = [
      '#availability span',
      '.a-color-success',
      '.a-color-state',
      '[data-automation-id="availability-message"]'
    ]

    for (const selector of selectors) {
      const availability = $(selector).first().text().trim()
      if (availability) return availability
    }

    return null
  }

  private static extractFeatures($: cheerio.CheerioAPI): string[] {
    const features: string[] = []
    
    $('#feature-bullets li, .a-unordered-list.a-vertical li').each((_, element) => {
      const feature = $(element).text().trim()
      if (feature && feature.length > 10 && !feature.toLowerCase().includes('make sure')) {
        features.push(feature)
      }
    })

    return features.slice(0, 5) // Limit to 5 features
  }

  static extractProductId(amazonUrl: string): string | null {
    // Extract ASIN from Amazon URL
    const patterns = [
      /\/dp\/([A-Z0-9]{10})/,
      /\/gp\/product\/([A-Z0-9]{10})/,
      /\/product\/([A-Z0-9]{10})/,
      /asin=([A-Z0-9]{10})/i
    ]

    for (const pattern of patterns) {
      const match = amazonUrl.match(pattern)
      if (match) return match[1]
    }

    return null
  }

  static isValidAmazonUrl(url: string): boolean {
    const amazonDomains = [
      'amazon.com',
      'amazon.co.uk',
      'amazon.de',
      'amazon.fr',
      'amazon.it',
      'amazon.es',
      'amazon.ca',
      'amazon.com.au',
      'amazon.in',
      'amazon.co.jp'
    ]

    try {
      const urlObj = new URL(url)
      return amazonDomains.some(domain => urlObj.hostname.includes(domain))
    } catch {
      return false
    }
  }

  static injectAffiliateTag(url: string, affiliateTag: string = 'shaf0bb-21'): string {
    try {
      const urlObj = new URL(url)

      // Remove existing tag parameter if present
      urlObj.searchParams.delete('tag')

      // Add our affiliate tag
      urlObj.searchParams.set('tag', affiliateTag)

      return urlObj.toString()
    } catch {
      // If URL parsing fails, try simple string manipulation
      const separator = url.includes('?') ? '&' : '?'
      return `${url}${separator}tag=${affiliateTag}`
    }
  }

  static cleanPrice(priceText: string): string | null {
    if (!priceText) return null

    // Remove currency symbols and extract numbers
    const cleaned = priceText
      .replace(/[^\d.,]/g, '')
      .replace(/,/g, '')
      .trim()

    // Validate that we have a valid number
    if (cleaned && !isNaN(parseFloat(cleaned))) {
      return parseFloat(cleaned).toString()
    }

    return null
  }
}
