// Auto-category mapping utility for product imports

interface Category {
  id: string
  name: string
}

interface CategoryMapping {
  keywords: string[]
  categoryNames: string[]
}

// Category mapping rules based on keywords
const categoryMappings: CategoryMapping[] = [
  {
    keywords: [
      'laptop', 'computer', 'pc', 'desktop', 'notebook', 'macbook',
      'phone', 'smartphone', 'mobile', 'iphone', 'android', 'samsung',
      'tablet', 'ipad', 'kindle', 'e-reader',
      'headphones', 'earphones', 'earbuds', 'speaker', 'bluetooth',
      'camera', 'dslr', 'lens', 'photography', 'video',
      'tv', 'television', 'monitor', 'display', 'screen',
      'gaming', 'console', 'playstation', 'xbox', 'nintendo',
      'charger', 'cable', 'adapter', 'power bank', 'battery',
      'mouse', 'keyboard', 'webcam', 'microphone',
      'smart watch', 'fitness tracker', 'wearable',
      'router', 'wifi', 'modem', 'network', 'ethernet'
    ],
    categoryNames: ['Electronics', 'Technology', 'Gadgets', 'Electronic']
  },
  {
    keywords: [
      'book', 'novel', 'textbook', 'ebook', 'paperback', 'hardcover',
      'fiction', 'non-fiction', 'biography', 'autobiography',
      'cookbook', 'recipe', 'cooking book',
      'children book', 'kids book', 'comic', 'manga',
      'educational', 'academic', 'study guide', 'reference',
      'poetry', 'literature', 'classic', 'bestseller',
      'self-help', 'motivational', 'business book',
      'history', 'science book', 'medical book'
    ],
    categoryNames: ['Books', 'Literature', 'Reading', 'Education']
  },
  {
    keywords: [
      'home', 'house', 'kitchen', 'dining', 'bedroom', 'living room',
      'furniture', 'chair', 'table', 'sofa', 'bed', 'mattress',
      'decor', 'decoration', 'wall art', 'painting', 'frame',
      'lighting', 'lamp', 'bulb', 'chandelier', 'led',
      'storage', 'organizer', 'shelf', 'cabinet', 'drawer',
      'appliance', 'microwave', 'refrigerator', 'oven', 'blender',
      'cookware', 'pan', 'pot', 'utensil', 'knife', 'cutting board',
      'garden', 'plant', 'flower', 'seed', 'pot', 'gardening',
      'outdoor', 'patio', 'lawn', 'yard', 'landscaping',
      'cleaning', 'vacuum', 'mop', 'detergent', 'soap'
    ],
    categoryNames: ['Home & Garden', 'Home', 'Garden', 'Furniture', 'Kitchen']
  },
  {
    keywords: [
      'sports', 'fitness', 'exercise', 'workout', 'gym', 'training',
      'running', 'jogging', 'marathon', 'athletic', 'sneakers',
      'football', 'soccer', 'basketball', 'tennis', 'golf',
      'swimming', 'cycling', 'bike', 'bicycle', 'helmet',
      'outdoor', 'camping', 'hiking', 'backpack', 'tent',
      'fishing', 'hunting', 'adventure', 'travel', 'luggage',
      'yoga', 'pilates', 'meditation', 'wellness', 'health',
      'protein', 'supplement', 'nutrition', 'vitamin',
      'equipment', 'gear', 'accessories', 'sporting goods'
    ],
    categoryNames: ['Sports & Outdoors', 'Sports', 'Fitness', 'Outdoor', 'Athletic']
  },
  {
    keywords: [
      'beauty', 'cosmetics', 'makeup', 'skincare', 'facial',
      'perfume', 'fragrance', 'cologne', 'scent',
      'hair', 'shampoo', 'conditioner', 'styling', 'haircare',
      'nail', 'polish', 'manicure', 'pedicure', 'salon',
      'health', 'wellness', 'supplement', 'vitamin', 'medicine',
      'personal care', 'hygiene', 'toothbrush', 'toothpaste',
      'lotion', 'cream', 'moisturizer', 'sunscreen', 'serum',
      'anti-aging', 'wrinkle', 'acne', 'treatment',
      'spa', 'massage', 'relaxation', 'aromatherapy'
    ],
    categoryNames: ['Health & Beauty', 'Beauty', 'Health', 'Personal Care', 'Cosmetics']
  },
  {
    keywords: [
      'clothing', 'shirt', 'pants', 'dress', 'skirt', 'jacket',
      'fashion', 'style', 'trendy', 'designer', 'brand',
      'shoes', 'boots', 'sandals', 'heels', 'sneakers',
      'accessories', 'bag', 'purse', 'wallet', 'belt',
      'jewelry', 'necklace', 'earrings', 'bracelet', 'ring',
      'watch', 'timepiece', 'luxury', 'premium',
      'men', 'women', 'kids', 'children', 'baby',
      'formal', 'casual', 'party', 'wedding', 'occasion'
    ],
    categoryNames: ['Fashion', 'Clothing', 'Apparel', 'Style', 'Accessories']
  },
  {
    keywords: [
      'toy', 'game', 'puzzle', 'board game', 'card game',
      'children', 'kids', 'baby', 'infant', 'toddler',
      'educational toy', 'learning', 'development',
      'action figure', 'doll', 'stuffed animal', 'plush',
      'building blocks', 'lego', 'construction', 'craft',
      'art supplies', 'coloring', 'drawing', 'painting',
      'outdoor toy', 'playground', 'swing', 'slide'
    ],
    categoryNames: ['Toys & Games', 'Toys', 'Games', 'Kids', 'Children']
  }
]

/**
 * Auto-map a product to a category based on its title and description
 */
export function autoMapCategory(
  productTitle: string, 
  productDescription: string | null, 
  availableCategories: Category[]
): string | null {
  const searchText = `${productTitle} ${productDescription || ''}`.toLowerCase()
  
  // Try to find a matching category mapping
  for (const mapping of categoryMappings) {
    // Check if any keywords match
    const hasKeywordMatch = mapping.keywords.some(keyword => 
      searchText.includes(keyword.toLowerCase())
    )
    
    if (hasKeywordMatch) {
      // Find the first available category that matches the mapping
      for (const categoryName of mapping.categoryNames) {
        const matchedCategory = availableCategories.find(cat => 
          cat.name.toLowerCase().includes(categoryName.toLowerCase()) ||
          categoryName.toLowerCase().includes(cat.name.toLowerCase())
        )
        
        if (matchedCategory) {
          return matchedCategory.id
        }
      }
    }
  }
  
  // If no specific match found, try to find a general category
  const generalCategories = ['General', 'Miscellaneous', 'Other', 'Products']
  for (const generalName of generalCategories) {
    const generalCategory = availableCategories.find(cat => 
      cat.name.toLowerCase().includes(generalName.toLowerCase())
    )
    if (generalCategory) {
      return generalCategory.id
    }
  }
  
  // Return the first available category as last resort
  return availableCategories.length > 0 ? availableCategories[0].id : null
}

/**
 * Get category suggestions for a product
 */
export function getCategorySuggestions(
  productTitle: string,
  productDescription: string | null,
  availableCategories: Category[]
): Category[] {
  const searchText = `${productTitle} ${productDescription || ''}`.toLowerCase()
  const suggestions: Category[] = []
  
  // Score categories based on keyword matches
  const categoryScores = new Map<string, number>()
  
  for (const mapping of categoryMappings) {
    let score = 0
    
    // Count keyword matches
    for (const keyword of mapping.keywords) {
      if (searchText.includes(keyword.toLowerCase())) {
        score += 1
      }
    }
    
    if (score > 0) {
      // Find matching categories
      for (const categoryName of mapping.categoryNames) {
        const matchedCategory = availableCategories.find(cat => 
          cat.name.toLowerCase().includes(categoryName.toLowerCase()) ||
          categoryName.toLowerCase().includes(cat.name.toLowerCase())
        )
        
        if (matchedCategory) {
          const currentScore = categoryScores.get(matchedCategory.id) || 0
          categoryScores.set(matchedCategory.id, currentScore + score)
        }
      }
    }
  }
  
  // Sort categories by score and return top suggestions
  const sortedCategories = Array.from(categoryScores.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map(([categoryId]) => availableCategories.find(cat => cat.id === categoryId))
    .filter(Boolean) as Category[]
  
  return sortedCategories
}

/**
 * Get a confidence score for category mapping (0-100)
 */
export function getCategoryMappingConfidence(
  productTitle: string,
  productDescription: string | null,
  categoryId: string,
  availableCategories: Category[]
): number {
  const category = availableCategories.find(cat => cat.id === categoryId)
  if (!category) return 0
  
  const searchText = `${productTitle} ${productDescription || ''}`.toLowerCase()
  let totalMatches = 0
  let totalKeywords = 0
  
  for (const mapping of categoryMappings) {
    const categoryMatches = mapping.categoryNames.some(name =>
      category.name.toLowerCase().includes(name.toLowerCase()) ||
      name.toLowerCase().includes(category.name.toLowerCase())
    )
    
    if (categoryMatches) {
      totalKeywords += mapping.keywords.length
      
      for (const keyword of mapping.keywords) {
        if (searchText.includes(keyword.toLowerCase())) {
          totalMatches += 1
        }
      }
    }
  }
  
  if (totalKeywords === 0) return 0
  
  return Math.round((totalMatches / totalKeywords) * 100)
}
