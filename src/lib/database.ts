import { query } from './postgres'

// Re-export query function for use in other modules
export { query }

// User types and functions
export interface User {
  id: string
  name: string
  email: string
  password_hash: string
  role: 'ADMIN' | 'USER'
  created_at: Date
  updated_at: Date
}

export interface Category {
  id: string
  name: string
  slug: string
  created_at: Date
  updated_at: Date
}

export interface Product {
  id: string
  name: string
  description: string | null
  amazon_affiliate_link: string
  image_url: string | null
  price: number | null
  mrp: number | null
  discount_percentage: number | null
  category_id: string
  created_by: string
  created_at: Date
  updated_at: Date
}

export interface Banner {
  id: string
  title: string
  image_url: string
  link_url: string | null
  start_date: Date
  end_date: Date | null
  is_active: boolean
  display_order: number
  created_at: Date
  updated_at: Date
  created_by: string
}

// User functions
export async function findUserByEmail(email: string): Promise<User | null> {
  const result = await query('SELECT * FROM users WHERE email = $1', [email])
  return result.rows[0] || null
}

export async function findUserById(id: string): Promise<User | null> {
  const result = await query('SELECT * FROM users WHERE id = $1', [id])
  return result.rows[0] || null
}

export async function createUser(userData: {
  name: string
  email: string
  password_hash: string
  role?: 'ADMIN' | 'USER'
}): Promise<User> {
  const result = await query(
    'INSERT INTO users (name, email, password_hash, role) VALUES ($1, $2, $3, $4) RETURNING *',
    [userData.name, userData.email, userData.password_hash, userData.role || 'USER']
  )
  return result.rows[0]
}

export async function countUsers(): Promise<number> {
  const result = await query('SELECT COUNT(*) as count FROM users')
  return parseInt(result.rows[0].count)
}

// Category functions
export async function getAllCategories(): Promise<Category[]> {
  const result = await query('SELECT * FROM categories ORDER BY name ASC')
  return result.rows
}

export async function getCategoriesWithProductCount(): Promise<(Category & { _count: { products: number } })[]> {
  const result = await query(`
    SELECT c.*, COUNT(p.id) as product_count 
    FROM categories c 
    LEFT JOIN products p ON c.id = p.category_id 
    GROUP BY c.id, c.name, c.slug, c.created_at, c.updated_at 
    ORDER BY c.name ASC
  `)
  
  return result.rows.map((row: any) => ({
    id: row.id,
    name: row.name,
    slug: row.slug,
    created_at: row.created_at,
    updated_at: row.updated_at,
    _count: { products: parseInt(row.product_count) }
  }))
}

export async function findCategoryById(id: string): Promise<Category | null> {
  const result = await query('SELECT * FROM categories WHERE id = $1', [id])
  return result.rows[0] || null
}

export async function findCategoryBySlug(slug: string): Promise<Category | null> {
  const result = await query('SELECT * FROM categories WHERE slug = $1', [slug])
  return result.rows[0] || null
}

export async function createCategory(categoryData: {
  name: string
  slug: string
}): Promise<Category> {
  const result = await query(
    'INSERT INTO categories (name, slug) VALUES ($1, $2) RETURNING *',
    [categoryData.name, categoryData.slug]
  )
  return result.rows[0]
}

export async function updateCategory(id: string, categoryData: {
  name: string
  slug: string
}): Promise<Category> {
  const result = await query(
    'UPDATE categories SET name = $1, slug = $2, updated_at = NOW() WHERE id = $3 RETURNING *',
    [categoryData.name, categoryData.slug, id]
  )
  return result.rows[0]
}

export async function deleteCategory(id: string): Promise<void> {
  await query('DELETE FROM categories WHERE id = $1', [id])
}

export async function countCategories(): Promise<number> {
  const result = await query('SELECT COUNT(*) as count FROM categories')
  return parseInt(result.rows[0].count)
}

// Product functions
export async function getAllProducts(options: {
  limit?: number
  offset?: number
  categoryId?: string
  search?: string
}): Promise<{ products: (Product & { category: Category; createdBy: { name: string } })[], total: number }> {
  let whereClause = 'WHERE 1=1'
  const params: any[] = []
  let paramIndex = 1

  if (options.categoryId) {
    whereClause += ` AND p.category_id = $${paramIndex}`
    params.push(options.categoryId)
    paramIndex++
  }

  if (options.search) {
    whereClause += ` AND (p.name ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`
    params.push(`%${options.search}%`)
    paramIndex++
  }

  // Get total count
  const countResult = await query(`
    SELECT COUNT(*) as count 
    FROM products p 
    ${whereClause}
  `, params)
  
  const total = parseInt(countResult.rows[0].count)

  // Get products with pagination
  let limitClause = ''
  if (options.limit) {
    limitClause += ` LIMIT $${paramIndex}`
    params.push(options.limit)
    paramIndex++
  }
  if (options.offset) {
    limitClause += ` OFFSET $${paramIndex}`
    params.push(options.offset)
  }

  const result = await query(`
    SELECT 
      p.*,
      c.id as category_id, c.name as category_name, c.slug as category_slug,
      u.name as created_by_name
    FROM products p
    JOIN categories c ON p.category_id = c.id
    JOIN users u ON p.created_by = u.id
    ${whereClause}
    ORDER BY p.created_at DESC
    ${limitClause}
  `, params)

  const products = result.rows.map((row: any) => ({
    id: row.id,
    name: row.name,
    description: row.description,
    amazonAffiliateLink: row.amazon_affiliate_link,
    imageUrl: row.image_url,
    price: row.price ? parseFloat(row.price) : null,
    mrp: row.mrp ? parseFloat(row.mrp) : null,
    discountPercentage: row.discount_percentage ? parseInt(row.discount_percentage) : null,
    category_id: row.category_id,
    created_by: row.created_by,
    created_at: row.created_at,
    updated_at: row.updated_at,
    category: {
      id: row.category_id,
      name: row.category_name,
      slug: row.category_slug,
      created_at: row.created_at,
      updated_at: row.updated_at
    },
    createdBy: {
      name: row.created_by_name
    }
  }))

  return { products, total }
}

export async function findProductById(id: string): Promise<(Product & { category: Category; createdBy: { name: string } }) | null> {
  const result = await query(`
    SELECT 
      p.*,
      c.id as category_id, c.name as category_name, c.slug as category_slug,
      u.name as created_by_name
    FROM products p
    JOIN categories c ON p.category_id = c.id
    JOIN users u ON p.created_by = u.id
    WHERE p.id = $1
  `, [id])

  if (!result.rows[0]) return null

  const row = result.rows[0]
  return {
    id: row.id,
    name: row.name,
    description: row.description,
    amazon_affiliate_link: row.amazon_affiliate_link,
    image_url: row.image_url,
    price: row.price ? parseFloat(row.price) : null,
    mrp: row.mrp ? parseFloat(row.mrp) : null,
    discount_percentage: row.discount_percentage ? parseInt(row.discount_percentage) : null,
    category_id: row.category_id,
    created_by: row.created_by,
    created_at: row.created_at,
    updated_at: row.updated_at,
    category: {
      id: row.category_id,
      name: row.category_name,
      slug: row.category_slug,
      created_at: row.created_at,
      updated_at: row.updated_at
    },
    createdBy: {
      name: row.created_by_name
    }
  }
}

// Enhanced duplicate detection with fuzzy matching
export async function checkDuplicateProduct(
  name: string,
  amazonLink: string,
  description?: string,
  imageUrl?: string,
  price?: number,
  categoryId?: string
) {
  // Helper function for fuzzy string matching
  const calculateSimilarity = (str1: string, str2: string): number => {
    const s1 = str1.toLowerCase().trim()
    const s2 = str2.toLowerCase().trim()

    if (s1 === s2) return 1.0

    // Remove common words and normalize
    const normalize = (str: string) => str
      .replace(/[^\w\s]/g, ' ')
      .replace(/\b(the|a|an|and|or|but|in|on|at|to|for|of|with|by)\b/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    const n1 = normalize(s1)
    const n2 = normalize(s2)

    if (n1 === n2) return 0.9

    // Check if one string contains the other (after normalization)
    if (n1.includes(n2) || n2.includes(n1)) return 0.8

    // Simple word overlap check
    const words1 = n1.split(' ').filter(w => w.length > 2)
    const words2 = n2.split(' ').filter(w => w.length > 2)

    if (words1.length === 0 || words2.length === 0) return 0

    const commonWords = words1.filter(w => words2.includes(w))
    const similarity = (commonWords.length * 2) / (words1.length + words2.length)

    return similarity
  }

  // Get all products for comparison
  let query_text = `SELECT id, name, amazon_affiliate_link, description, image_url, price, category_id FROM products`
  let params: any[] = []

  // If category is specified, check within that category only
  if (categoryId) {
    query_text += ` WHERE category_id = $1`
    params.push(categoryId)
  }

  const result = await query(query_text, params)
  const products = result.rows

  // Check for duplicates using multiple criteria
  for (const product of products) {
    const duplicateReasons: string[] = []

    // 1. Exact Amazon link match (highest priority)
    if (amazonLink && product.amazon_affiliate_link === amazonLink) {
      duplicateReasons.push('Amazon link')
    }

    // 2. High similarity in product name
    const nameSimilarity = calculateSimilarity(name, product.name)
    if (nameSimilarity >= 0.8) {
      duplicateReasons.push(`Product name (${Math.round(nameSimilarity * 100)}% similar)`)
    }

    // 3. Additional criteria for products with similar names
    if (nameSimilarity >= 0.7) {
      // Check description similarity
      if (description && product.description) {
        const descSimilarity = calculateSimilarity(description, product.description)
        if (descSimilarity >= 0.8) {
          duplicateReasons.push(`Description (${Math.round(descSimilarity * 100)}% similar)`)
        }
      }

      // Check image URL match
      if (imageUrl && product.image_url && imageUrl === product.image_url) {
        duplicateReasons.push('Image URL')
      }

      // Check price match (within 5% tolerance)
      if (price && product.price) {
        const priceDiff = Math.abs(price - product.price) / product.price
        if (priceDiff <= 0.05) {
          duplicateReasons.push('Price')
        }
      }
    }

    // Consider duplicate if we have strong evidence
    if (duplicateReasons.length >= 1 && (
      duplicateReasons.includes('Amazon link') ||
      nameSimilarity >= 0.9 ||
      (nameSimilarity >= 0.7 && duplicateReasons.length >= 2)
    )) {
      return {
        ...product,
        duplicateReasons
      }
    }
  }

  return null
}

export async function createProduct(productData: {
  name: string
  description?: string
  amazon_affiliate_link: string
  image_url?: string
  price?: number
  mrp?: number
  discount_percentage?: number
  category_id: string
  created_by: string
}): Promise<Product> {
  const result = await query(
    'INSERT INTO products (name, description, amazon_affiliate_link, image_url, price, mrp, discount_percentage, category_id, created_by) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *',
    [
      productData.name,
      productData.description || null,
      productData.amazon_affiliate_link,
      productData.image_url || null,
      productData.price || null,
      productData.mrp || null,
      productData.discount_percentage || null,
      productData.category_id,
      productData.created_by
    ]
  )
  return result.rows[0]
}

export async function updateProduct(id: string, productData: Partial<{
  name: string
  description: string
  amazon_affiliate_link: string
  image_url: string
  price: number
  mrp: number
  discount_percentage: number
  category_id: string
}>): Promise<Product> {
  const fields = []
  const values = []
  let paramIndex = 1

  Object.entries(productData).forEach(([key, value]) => {
    if (value !== undefined) {
      fields.push(`${key} = $${paramIndex}`)
      values.push(value)
      paramIndex++
    }
  })

  fields.push(`updated_at = NOW()`)
  values.push(id)

  const result = await query(
    `UPDATE products SET ${fields.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
    values
  )
  return result.rows[0]
}

export async function deleteProduct(id: string): Promise<void> {
  await query('DELETE FROM products WHERE id = $1', [id])
}

export async function countProducts(): Promise<number> {
  const result = await query('SELECT COUNT(*) as count FROM products')
  return parseInt(result.rows[0].count)
}

// Banner CRUD operations
export async function getAllBanners(): Promise<Banner[]> {
  const result = await query(`
    SELECT * FROM banners
    WHERE is_active = true
    AND (start_date IS NULL OR start_date <= NOW())
    AND (end_date IS NULL OR end_date >= NOW())
    ORDER BY display_order ASC, created_at DESC
  `)
  return result.rows
}

export async function getAllBannersAdmin(): Promise<Banner[]> {
  const result = await query(`
    SELECT * FROM banners
    ORDER BY display_order ASC, created_at DESC
  `)
  return result.rows
}

export async function findBannerById(id: string): Promise<Banner | null> {
  const result = await query('SELECT * FROM banners WHERE id = $1', [id])
  return result.rows[0] || null
}

export async function createBanner(bannerData: {
  title: string
  image_url: string
  link_url?: string
  start_date?: Date
  end_date?: Date
  is_active?: boolean
  display_order?: number
  created_by: string
}): Promise<Banner> {
  const id = `banner_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const result = await query(
    `INSERT INTO banners (id, title, image_url, link_url, start_date, end_date, is_active, display_order, created_by, created_at, updated_at)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
     RETURNING *`,
    [
      id,
      bannerData.title,
      bannerData.image_url,
      bannerData.link_url || null,
      bannerData.start_date || new Date(),
      bannerData.end_date || null,
      bannerData.is_active !== false,
      bannerData.display_order || 0,
      bannerData.created_by
    ]
  )

  return result.rows[0]
}

export async function updateBanner(id: string, bannerData: Partial<{
  title: string
  image_url: string
  link_url: string
  start_date: Date
  end_date: Date
  is_active: boolean
  display_order: number
}>): Promise<Banner> {
  const fields = []
  const values = []
  let paramIndex = 1

  Object.entries(bannerData).forEach(([key, value]) => {
    if (value !== undefined) {
      fields.push(`${key} = $${paramIndex}`)
      values.push(value)
      paramIndex++
    }
  })

  if (fields.length === 0) {
    throw new Error('No fields to update')
  }

  fields.push(`updated_at = NOW()`)
  values.push(id)

  const result = await query(
    `UPDATE banners SET ${fields.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
    values
  )

  if (result.rows.length === 0) {
    throw new Error('Banner not found')
  }

  return result.rows[0]
}

export async function deleteBanner(id: string): Promise<boolean> {
  const result = await query('DELETE FROM banners WHERE id = $1', [id])
  return result.rowCount > 0
}
