import nodemailer from 'nodemailer'
import { query } from './database'

interface EmailSettings {
  id: string
  smtp_host: string
  smtp_port: number
  smtp_user: string
  smtp_password: string
  smtp_secure: boolean
  from_email: string
  from_name: string
  is_active: boolean
}

interface EmailSubscriber {
  id: string
  email: string
  name?: string
  subscribed: boolean
}

interface EmailTemplate {
  id: string
  name: string
  subject: string
  html_content: string
  template_type: string
}

interface EmailCampaign {
  id: string
  name: string
  subject: string
  html_content: string
  template_id?: string
  status: 'draft' | 'sending' | 'sent' | 'failed'
  recipient_count: number
  sent_count: number
  delivered_count: number
  opened_count: number
  clicked_count: number
}

interface Product {
  id: string
  name: string
  price?: number
  mrp?: number
  discountPercentage?: number
  imageUrl?: string
  amazonAffiliateLink: string
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null
  private settings: EmailSettings | null = null

  async initialize(): Promise<boolean> {
    try {
      const result = await query('SELECT * FROM email_settings WHERE is_active = true LIMIT 1')
      
      if (result.rows.length === 0) {
        console.log('No active email settings found')
        return false
      }

      this.settings = result.rows[0]
      
      this.transporter = nodemailer.createTransport({
        host: this.settings!.smtp_host,
        port: this.settings!.smtp_port,
        secure: this.settings!.smtp_secure,
        auth: {
          user: this.settings!.smtp_user,
          pass: this.settings!.smtp_password,
        },
      })

      return true
    } catch (error) {
      console.error('Failed to initialize email service:', error)
      return false
    }
  }

  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      if (!await this.initialize()) {
        return { success: false, message: 'Email service not configured' }
      }

      await this.transporter!.verify()
      return { success: true, message: 'Email service connected successfully' }
    } catch (error) {
      return { 
        success: false, 
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      }
    }
  }

  async sendTestEmail(toEmail: string): Promise<boolean> {
    try {
      if (!await this.initialize()) {
        return false
      }

      const testHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #f97316, #ea580c); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">🛍️ Amazing Deals</h1>
            <p style="color: white; margin: 10px 0 0 0;">Email Test Successful!</p>
          </div>
          <div style="padding: 30px;">
            <h2>Email Configuration Test</h2>
            <p>Congratulations! Your email service is configured correctly.</p>
            <p>This test email confirms that:</p>
            <ul>
              <li>✅ SMTP settings are correct</li>
              <li>✅ Authentication is working</li>
              <li>✅ Email delivery is functional</li>
            </ul>
            <p>You can now send marketing campaigns to your subscribers!</p>
          </div>
          <div style="background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
            <p>© 2024 Amazing Deals. All rights reserved.</p>
          </div>
        </div>
      `

      await this.transporter!.sendMail({
        from: `${this.settings!.from_name} <${this.settings!.from_email}>`,
        to: toEmail,
        subject: 'Amazing Deals - Email Test Successful',
        html: testHtml,
      })

      return true
    } catch (error) {
      console.error('Failed to send test email:', error)
      return false
    }
  }

  async getSubscribers(): Promise<EmailSubscriber[]> {
    try {
      const result = await query('SELECT * FROM email_subscribers WHERE subscribed = true ORDER BY created_at DESC')
      return result.rows
    } catch (error) {
      console.error('Failed to get subscribers:', error)
      return []
    }
  }

  async addSubscriber(email: string, name?: string, source: string = 'manual'): Promise<boolean> {
    try {
      await query(
        'INSERT INTO email_subscribers (email, name, subscription_source) VALUES ($1, $2, $3) ON CONFLICT (email) DO UPDATE SET subscribed = true, updated_at = CURRENT_TIMESTAMP',
        [email, name, source]
      )
      return true
    } catch (error) {
      console.error('Failed to add subscriber:', error)
      return false
    }
  }

  async removeSubscriber(email: string): Promise<boolean> {
    try {
      await query(
        'UPDATE email_subscribers SET subscribed = false, updated_at = CURRENT_TIMESTAMP WHERE email = $1',
        [email]
      )
      return true
    } catch (error) {
      console.error('Failed to remove subscriber:', error)
      return false
    }
  }

  async importSubscribers(subscribers: { email: string; name?: string }[]): Promise<{ success: number; failed: number }> {
    let success = 0
    let failed = 0

    for (const subscriber of subscribers) {
      if (await this.addSubscriber(subscriber.email, subscriber.name, 'csv_import')) {
        success++
      } else {
        failed++
      }
    }

    return { success, failed }
  }

  async getTemplates(): Promise<EmailTemplate[]> {
    try {
      const result = await query('SELECT * FROM email_templates ORDER BY created_at DESC')
      return result.rows
    } catch (error) {
      console.error('Failed to get templates:', error)
      return []
    }
  }

  async createTemplate(name: string, subject: string, htmlContent: string, templateType: string = 'custom'): Promise<string | null> {
    try {
      const result = await query(
        'INSERT INTO email_templates (name, subject, html_content, template_type) VALUES ($1, $2, $3, $4) RETURNING id',
        [name, subject, htmlContent, templateType]
      )
      return result.rows[0].id
    } catch (error) {
      console.error('Failed to create template:', error)
      return null
    }
  }

  async createCampaign(name: string, subject: string, htmlContent: string, templateId?: string): Promise<string | null> {
    try {
      const result = await query(
        'INSERT INTO email_campaigns (name, subject, html_content, template_id) VALUES ($1, $2, $3, $4) RETURNING id',
        [name, subject, htmlContent, templateId]
      )
      return result.rows[0].id
    } catch (error) {
      console.error('Failed to create campaign:', error)
      return null
    }
  }

  async sendCampaign(campaignId: string): Promise<boolean> {
    try {
      if (!await this.initialize()) {
        return false
      }

      // Get campaign details
      const campaignResult = await query('SELECT * FROM email_campaigns WHERE id = $1', [campaignId])
      if (campaignResult.rows.length === 0) {
        throw new Error('Campaign not found')
      }

      const campaign = campaignResult.rows[0]
      
      // Get subscribers
      const subscribers = await this.getSubscribers()
      if (subscribers.length === 0) {
        throw new Error('No subscribers found')
      }

      // Update campaign status
      await query(
        'UPDATE email_campaigns SET status = $1, recipient_count = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
        ['sending', subscribers.length, campaignId]
      )

      let sentCount = 0
      let deliveredCount = 0

      // Send emails to subscribers
      for (const subscriber of subscribers) {
        try {
          // Create campaign recipient record
          await query(
            'INSERT INTO email_campaign_recipients (campaign_id, subscriber_id, email, status) VALUES ($1, $2, $3, $4)',
            [campaignId, subscriber.id, subscriber.email, 'sending']
          )

          // Replace template variables
          const personalizedContent = this.personalizeContent(campaign.html_content, subscriber)

          // Send email
          await this.transporter!.sendMail({
            from: `${this.settings!.from_name} <${this.settings!.from_email}>`,
            to: subscriber.email,
            subject: campaign.subject,
            html: personalizedContent,
          })

          // Update recipient status
          await query(
            'UPDATE email_campaign_recipients SET status = $1, sent_at = CURRENT_TIMESTAMP WHERE campaign_id = $2 AND subscriber_id = $3',
            ['sent', campaignId, subscriber.id]
          )

          sentCount++
          deliveredCount++

          // Small delay to avoid overwhelming SMTP server
          await new Promise(resolve => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscriber.email}:`, error)
          
          // Update recipient status with error
          await query(
            'UPDATE email_campaign_recipients SET status = $1, error_message = $2 WHERE campaign_id = $3 AND subscriber_id = $4',
            ['failed', error instanceof Error ? error.message : 'Unknown error', campaignId, subscriber.id]
          )
        }
      }

      // Update campaign with final stats
      await query(
        'UPDATE email_campaigns SET status = $1, sent_count = $2, delivered_count = $3, sent_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = $4',
        ['sent', sentCount, deliveredCount, campaignId]
      )

      return true
    } catch (error) {
      console.error('Failed to send campaign:', error)
      
      // Update campaign status to failed
      await query(
        'UPDATE email_campaigns SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['failed', campaignId]
      )
      
      return false
    }
  }

  private personalizeContent(content: string, subscriber: EmailSubscriber): string {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    
    return content
      .replace(/{{subscriber_name}}/g, subscriber.name || 'Valued Customer')
      .replace(/{{subscriber_email}}/g, subscriber.email)
      .replace(/{{store_url}}/g, baseUrl)
      .replace(/{{unsubscribe_url}}/g, `${baseUrl}/unsubscribe?email=${encodeURIComponent(subscriber.email)}`)
  }

  async generateProductEmailContent(products: Product[]): Promise<string> {
    let productHtml = ''
    
    for (const product of products) {
      const discountBadge = product.discountPercentage 
        ? `<span class="discount">${product.discountPercentage}% OFF</span>` 
        : ''
      
      const priceHtml = product.price 
        ? `<div class="price">₹${product.price.toLocaleString()}${product.mrp && product.mrp > product.price ? ` <span class="old-price">₹${product.mrp.toLocaleString()}</span>` : ''}</div>`
        : ''

      productHtml += `
        <div class="product">
          ${product.imageUrl ? `<img src="${product.imageUrl}" alt="${product.name}" style="max-width: 200px; height: auto; float: left; margin-right: 20px;">` : ''}
          <h3>${product.name}</h3>
          ${priceHtml}
          ${discountBadge}
          <p style="margin: 15px 0;">
            <a href="${product.amazonAffiliateLink}" class="button">Buy Now on Amazon</a>
          </p>
          <div style="clear: both;"></div>
        </div>
      `
    }
    
    return productHtml
  }

  async getCampaigns(): Promise<EmailCampaign[]> {
    try {
      const result = await query('SELECT * FROM email_campaigns ORDER BY created_at DESC')
      return result.rows
    } catch (error) {
      console.error('Failed to get campaigns:', error)
      return []
    }
  }

  async updateEmailSettings(settings: Partial<EmailSettings>): Promise<boolean> {
    try {
      const updateFields = []
      const values = []
      let paramIndex = 1

      for (const [key, value] of Object.entries(settings)) {
        if (value !== undefined) {
          updateFields.push(`${key} = $${paramIndex}`)
          values.push(value)
          paramIndex++
        }
      }

      if (updateFields.length === 0) {
        return false
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP')

      await query(
        `UPDATE email_settings SET ${updateFields.join(', ')} WHERE id = (SELECT id FROM email_settings LIMIT 1)`,
        values
      )

      return true
    } catch (error) {
      console.error('Failed to update email settings:', error)
      return false
    }
  }
}

export const emailService = new EmailService()
export type { EmailSubscriber, EmailTemplate, EmailCampaign, EmailSettings }
