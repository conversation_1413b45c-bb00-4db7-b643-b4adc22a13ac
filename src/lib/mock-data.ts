// Mock data for testing when database is not available

export const mockCategories = [
  {
    id: 'cat_electronics',
    name: 'Electronics',
    slug: 'electronics',
    created_at: new Date(),
    updated_at: new Date(),
    _count: { products: 2 }
  },
  {
    id: 'cat_books',
    name: 'Books',
    slug: 'books',
    created_at: new Date(),
    updated_at: new Date(),
    _count: { products: 2 }
  },
  {
    id: 'cat_home_garden',
    name: 'Home & Garden',
    slug: 'home-garden',
    created_at: new Date(),
    updated_at: new Date(),
    _count: { products: 1 }
  },
  {
    id: 'cat_sports',
    name: 'Sports & Outdoors',
    slug: 'sports-outdoors',
    created_at: new Date(),
    updated_at: new Date(),
    _count: { products: 1 }
  },
  {
    id: 'cat_health_beauty',
    name: 'Health & Beauty',
    slug: 'health-beauty',
    created_at: new Date(),
    updated_at: new Date(),
    _count: { products: 1 }
  }
]

export const mockProducts = [
  {
    id: 'prod_iphone15',
    name: 'iPhone 15 Pro',
    description: 'Latest iPhone with advanced camera system and A17 Pro chip',
    amazon_affiliate_link: 'https://amazon.com/dp/B0CHX1W1XY?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',
    price: 999.99,
    category_id: 'cat_electronics',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_electronics',
      name: 'Electronics',
      slug: 'electronics',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  },
  {
    id: 'prod_macbook',
    name: 'MacBook Air M3',
    description: 'Powerful laptop with M3 chip and all-day battery life',
    amazon_affiliate_link: 'https://amazon.com/dp/B0CX23V2ZK?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400',
    price: 1299.99,
    category_id: 'cat_electronics',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_electronics',
      name: 'Electronics',
      slug: 'electronics',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  },
  {
    id: 'prod_psychology_money',
    name: 'The Psychology of Money',
    description: 'Timeless lessons on wealth, greed, and happiness',
    amazon_affiliate_link: 'https://amazon.com/dp/0857197681?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400',
    price: 14.99,
    category_id: 'cat_books',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_books',
      name: 'Books',
      slug: 'books',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  },
  {
    id: 'prod_atomic_habits',
    name: 'Atomic Habits',
    description: 'An easy & proven way to build good habits & break bad ones',
    amazon_affiliate_link: 'https://amazon.com/dp/0735211299?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
    price: 13.99,
    category_id: 'cat_books',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_books',
      name: 'Books',
      slug: 'books',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  },
  {
    id: 'prod_robot_vacuum',
    name: 'Robot Vacuum Cleaner',
    description: 'Smart robot vacuum with mapping and app control',
    amazon_affiliate_link: 'https://amazon.com/dp/B08XYQZQZX?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
    price: 299.99,
    category_id: 'cat_home_garden',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_home_garden',
      name: 'Home & Garden',
      slug: 'home-garden',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  },
  {
    id: 'prod_yoga_mat',
    name: 'Yoga Mat Premium',
    description: 'Non-slip exercise mat for yoga, pilates, and fitness',
    amazon_affiliate_link: 'https://amazon.com/dp/B01LXQZ8ZX?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
    price: 39.99,
    category_id: 'cat_sports',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_sports',
      name: 'Sports & Outdoors',
      slug: 'sports-outdoors',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  },
  {
    id: 'prod_skincare_set',
    name: 'Skincare Set',
    description: 'Complete skincare routine with cleanser, serum, and moisturizer',
    amazon_affiliate_link: 'https://amazon.com/dp/B08XYZABC1?tag=youraffid-20',
    image_url: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=400',
    price: 79.99,
    category_id: 'cat_health_beauty',
    created_by: 'admin_user_001',
    created_at: new Date(),
    updated_at: new Date(),
    category: {
      id: 'cat_health_beauty',
      name: 'Health & Beauty',
      slug: 'health-beauty',
      created_at: new Date(),
      updated_at: new Date()
    },
    createdBy: {
      name: 'Admin User'
    }
  }
]

export const mockUser = {
  id: 'admin_user_001',
  name: 'Admin User',
  email: '<EMAIL>',
  password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.LG7my', // admin123
  role: 'ADMIN' as const,
  created_at: new Date(),
  updated_at: new Date()
}
