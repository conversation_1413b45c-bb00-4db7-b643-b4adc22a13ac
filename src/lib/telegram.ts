interface TelegramConfig {
  botToken: string
  channelId: string
  enabled: boolean
}

interface ProductNotification {
  id: string
  name: string
  description?: string
  price?: number
  mrp?: number
  discountPercentage?: number
  category: string
  amazonAffiliateLink: string
  imageUrl?: string
  isNew?: boolean
  isUpdate?: boolean
}

class TelegramService {
  private config: TelegramConfig

  constructor() {
    this.config = {
      botToken: process.env.TELEGRAM_BOT_TOKEN || '',
      channelId: process.env.TELEGRAM_CHANNEL_ID || '',
      enabled: process.env.TELEGRAM_NOTIFICATIONS_ENABLED === 'true'
    }
  }

  private isConfigured(): boolean {
    return !!(this.config.botToken && this.config.channelId && this.config.enabled)
  }

  private formatPrice(price: number): string {
    return `₹${price.toLocaleString('en-IN')}`
  }

  private calculateDiscount(price: number, mrp: number): number {
    return Math.round(((mrp - price) / mrp) * 100)
  }

  private createProductMessage(product: ProductNotification): string {
    const { name, description, price, mrp, category, amazonAffiliateLink, isNew, isUpdate } = product
    
    // Header with emoji based on action
    const header = isNew ? '🆕 NEW PRODUCT ADDED!' : isUpdate ? '🔄 PRODUCT UPDATED!' : '📦 AMAZING DEALS'
    
    // Price information
    let priceInfo = ''
    if (price) {
      priceInfo = `💰 **Price:** ${this.formatPrice(price)}`
      
      if (mrp && mrp > price) {
        const discount = this.calculateDiscount(price, mrp)
        priceInfo += `\n💸 **MRP:** ~${this.formatPrice(mrp)}~`
        priceInfo += `\n🎯 **Discount:** ${discount}% OFF`
        priceInfo += `\n💵 **You Save:** ${this.formatPrice(mrp - price)}`
      }
    }

    // Build the complete message
    const message = `
${header}

🛍️ **${name}**

${description ? `📝 ${description}\n` : ''}
🏷️ **Category:** ${category}

${priceInfo}

🔗 **Buy Now:** [Get Best Deal on Amazon](${amazonAffiliateLink})

🌟 *Amazing Deals - Your trusted shopping companion*
#AmazingDeals #Shopping #Deals #Amazon
    `.trim()

    return message
  }

  private async sendMessage(message: string, imageUrl?: string): Promise<boolean> {
    if (!this.isConfigured()) {
      console.log('⚠️ Telegram not configured, skipping notification')
      return false
    }

    try {
      const baseUrl = `https://api.telegram.org/bot${this.config.botToken}`
      
      if (imageUrl) {
        // Send photo with caption
        const response = await fetch(`${baseUrl}/sendPhoto`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            chat_id: this.config.channelId,
            photo: imageUrl,
            caption: message,
            parse_mode: 'Markdown',
            disable_web_page_preview: false
          })
        })

        if (!response.ok) {
          throw new Error(`Telegram API error: ${response.status}`)
        }
      } else {
        // Send text message only
        const response = await fetch(`${baseUrl}/sendMessage`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            chat_id: this.config.channelId,
            text: message,
            parse_mode: 'Markdown',
            disable_web_page_preview: false
          })
        })

        if (!response.ok) {
          throw new Error(`Telegram API error: ${response.status}`)
        }
      }

      console.log('✅ Telegram notification sent successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to send Telegram notification:', error)
      return false
    }
  }

  async notifyNewProduct(product: ProductNotification): Promise<boolean> {
    if (!this.isConfigured()) {
      return false
    }

    console.log('📱 Sending new product notification to Telegram...')
    
    const message = this.createProductMessage({ ...product, isNew: true })
    return await this.sendMessage(message, product.imageUrl)
  }

  async notifyProductUpdate(product: ProductNotification): Promise<boolean> {
    if (!this.isConfigured()) {
      return false
    }

    console.log('📱 Sending product update notification to Telegram...')
    
    const message = this.createProductMessage({ ...product, isUpdate: true })
    return await this.sendMessage(message, product.imageUrl)
  }

  async notifyBulkImport(products: ProductNotification[], source: string = 'Amazon'): Promise<boolean> {
    if (!this.isConfigured() || products.length === 0) {
      return false
    }

    console.log(`📱 Sending bulk import notification to Telegram (${products.length} products)...`)

    const message = `
🚀 **BULK IMPORT COMPLETED!**

📦 **${products.length} new products** added from ${source}

🏷️ **Categories:**
${[...new Set(products.map(p => p.category))].map(cat => `• ${cat}`).join('\n')}

💰 **Price Range:**
₹${Math.min(...products.filter(p => p.price).map(p => p.price!)).toLocaleString()} - ₹${Math.max(...products.filter(p => p.price).map(p => p.price!)).toLocaleString()}

🔗 **Check out all new products:** [Amazing Deals Store](${process.env.NEXTAUTH_URL || 'http://localhost:3000'})

🌟 *Amazing Deals - Your trusted shopping companion*
#BulkImport #NewProducts #AmazingDeals
    `.trim()

    return await this.sendMessage(message)
  }

  async testConnection(): Promise<{ success: boolean; message: string }> {
    if (!this.isConfigured()) {
      return {
        success: false,
        message: 'Telegram not configured. Please check environment variables.'
      }
    }

    try {
      const baseUrl = `https://api.telegram.org/bot${this.config.botToken}`
      const response = await fetch(`${baseUrl}/getMe`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      
      if (data.ok) {
        return {
          success: true,
          message: `Connected to bot: ${data.result.first_name} (@${data.result.username})`
        }
      } else {
        throw new Error(data.description || 'Unknown error')
      }
    } catch (error) {
      return {
        success: false,
        message: `Connection failed: ${error}`
      }
    }
  }

  getConfig(): Partial<TelegramConfig> {
    return {
      enabled: this.config.enabled,
      channelId: this.config.channelId ? this.config.channelId.substring(0, 10) + '...' : 'Not set'
    }
  }
}

// Export singleton instance
export const telegramService = new TelegramService()

// Export types for use in other files
export type { ProductNotification, TelegramConfig }
