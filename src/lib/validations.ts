import { z } from 'zod'

// Auth schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

export const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

// Category schemas
export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  slug: z.string().min(1, 'Category slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
})

// Product schemas
export const productSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(1000, 'Product name too long'),
  description: z.string().optional(),
  amazonAffiliateLink: z.string().url('Invalid Amazon affiliate link'),
  imageUrl: z.string().url('Invalid image URL').optional().or(z.literal('')),
  price: z.number().positive('Price must be positive').optional(),
  mrp: z.number().positive('MRP must be positive').optional(),
  discountPercentage: z.number().min(0).max(100, 'Discount must be between 0-100%').optional(),
  categoryId: z.string().min(1, 'Category is required'),
})

export const updateProductSchema = productSchema.partial()

// Search schema
export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  category: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
})

export type LoginInput = z.infer<typeof loginSchema>
export type RegisterInput = z.infer<typeof registerSchema>
export type CategoryInput = z.infer<typeof categorySchema>
export type ProductInput = z.infer<typeof productSchema>
export type UpdateProductInput = z.infer<typeof updateProductSchema>
export type SearchInput = z.infer<typeof searchSchema>
