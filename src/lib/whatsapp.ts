import { query } from './database'

interface WhatsAppSettings {
  id: string
  business_account_id: string
  access_token: string
  phone_number_id: string
  webhook_verify_token: string
  webhook_url: string
  is_active: boolean
}

interface WhatsAppSubscriber {
  id: string
  phone_number: string
  name?: string
  subscribed: boolean
  opt_in_date: string
}

interface WhatsAppTemplate {
  id: string
  name: string
  template_name: string
  language: string
  category: string
  body_text: string
  footer_text?: string
  status: string
}

interface WhatsAppCampaign {
  id: string
  name: string
  template_id?: string
  template_name?: string
  message_content: string
  status: 'draft' | 'sending' | 'sent' | 'failed'
  recipient_count: number
  sent_count: number
  delivered_count: number
  read_count: number
}

interface Product {
  id: string
  name: string
  price?: number
  mrp?: number
  discountPercentage?: number
  amazonAffiliateLink: string
}

class WhatsAppService {
  private settings: WhatsAppSettings | null = null

  async initialize(): Promise<boolean> {
    try {
      const result = await query('SELECT * FROM whatsapp_settings WHERE is_active = true LIMIT 1')
      
      if (result.rows.length === 0) {
        console.log('No active WhatsApp settings found')
        return false
      }

      this.settings = result.rows[0]
      return true
    } catch (error) {
      console.error('Failed to initialize WhatsApp service:', error)
      return false
    }
  }

  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      if (!await this.initialize()) {
        return { success: false, message: 'WhatsApp service not configured' }
      }

      // Test WhatsApp Business API connection
      const response = await fetch(`https://graph.facebook.com/v18.0/${this.settings!.business_account_id}`, {
        headers: {
          'Authorization': `Bearer ${this.settings!.access_token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        return { 
          success: true, 
          message: `Connected to WhatsApp Business: ${data.name || 'Account verified'}` 
        }
      } else {
        const error = await response.json()
        return { 
          success: false, 
          message: `Connection failed: ${error.error?.message || 'Unknown error'}` 
        }
      }
    } catch (error) {
      return { 
        success: false, 
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      }
    }
  }

  async sendMessage(phoneNumber: string, message: string, templateName?: string): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'WhatsApp service not configured' }
      }

      // Clean phone number (remove any non-digits except +)
      const cleanPhone = phoneNumber.replace(/[^\d+]/g, '')
      
      // Prepare message payload
      const payload: any = {
        messaging_product: 'whatsapp',
        to: cleanPhone,
        type: 'text',
        text: {
          body: message
        }
      }

      // If using a template, modify payload structure
      if (templateName) {
        payload.type = 'template'
        payload.template = {
          name: templateName,
          language: {
            code: 'en'
          }
        }
        delete payload.text
      }

      const response = await fetch(`https://graph.facebook.com/v18.0/${this.settings!.phone_number_id}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.settings!.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        const data = await response.json()
        
        // Log the message
        await this.logMessage(data.messages[0].id, cleanPhone, 'text', message, 'sent')
        
        return { 
          success: true, 
          messageId: data.messages[0].id 
        }
      } else {
        const error = await response.json()
        await this.logMessage(null, cleanPhone, 'text', message, 'failed')
        
        return { 
          success: false, 
          error: error.error?.message || 'Failed to send message' 
        }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  async sendTemplateMessage(phoneNumber: string, templateName: string, parameters: string[]): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'WhatsApp service not configured' }
      }

      const cleanPhone = phoneNumber.replace(/[^\d+]/g, '')
      
      const payload = {
        messaging_product: 'whatsapp',
        to: cleanPhone,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'en'
          },
          components: [
            {
              type: 'body',
              parameters: parameters.map(param => ({
                type: 'text',
                text: param
              }))
            }
          ]
        }
      }

      const response = await fetch(`https://graph.facebook.com/v18.0/${this.settings!.phone_number_id}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.settings!.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        const data = await response.json()
        
        await this.logMessage(data.messages[0].id, cleanPhone, 'template', templateName, 'sent')
        
        return { 
          success: true, 
          messageId: data.messages[0].id 
        }
      } else {
        const error = await response.json()
        await this.logMessage(null, cleanPhone, 'template', templateName, 'failed')
        
        return { 
          success: false, 
          error: error.error?.message || 'Failed to send template message' 
        }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  async getSubscribers(): Promise<WhatsAppSubscriber[]> {
    try {
      const result = await query('SELECT * FROM whatsapp_subscribers WHERE subscribed = true ORDER BY created_at DESC')
      return result.rows
    } catch (error) {
      console.error('Failed to get WhatsApp subscribers:', error)
      return []
    }
  }

  async addSubscriber(phoneNumber: string, name?: string, source: string = 'manual'): Promise<boolean> {
    try {
      const cleanPhone = phoneNumber.replace(/[^\d+]/g, '')
      
      await query(
        'INSERT INTO whatsapp_subscribers (phone_number, name, subscription_source) VALUES ($1, $2, $3) ON CONFLICT (phone_number) DO UPDATE SET subscribed = true, updated_at = CURRENT_TIMESTAMP',
        [cleanPhone, name, source]
      )
      return true
    } catch (error) {
      console.error('Failed to add WhatsApp subscriber:', error)
      return false
    }
  }

  async removeSubscriber(phoneNumber: string): Promise<boolean> {
    try {
      const cleanPhone = phoneNumber.replace(/[^\d+]/g, '')
      
      await query(
        'UPDATE whatsapp_subscribers SET subscribed = false, opt_out_date = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE phone_number = $1',
        [cleanPhone]
      )
      return true
    } catch (error) {
      console.error('Failed to remove WhatsApp subscriber:', error)
      return false
    }
  }

  async notifyNewProduct(product: Product): Promise<boolean> {
    try {
      const subscribers = await this.getSubscribers()
      if (subscribers.length === 0) return false

      const message = this.formatProductMessage(product, 'new')
      let successCount = 0

      for (const subscriber of subscribers) {
        const result = await this.sendMessage(subscriber.phone_number, message)
        if (result.success) {
          successCount++
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      console.log(`WhatsApp: Sent new product notification to ${successCount}/${subscribers.length} subscribers`)
      return successCount > 0
    } catch (error) {
      console.error('Failed to send WhatsApp product notification:', error)
      return false
    }
  }

  async notifyPriceDrop(product: Product, oldPrice: number): Promise<boolean> {
    try {
      const subscribers = await this.getSubscribers()
      if (subscribers.length === 0) return false

      const savings = oldPrice - (product.price || 0)
      const message = this.formatProductMessage(product, 'price_drop', { oldPrice, savings })
      let successCount = 0

      for (const subscriber of subscribers) {
        const result = await this.sendMessage(subscriber.phone_number, message)
        if (result.success) {
          successCount++
        }
        
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      console.log(`WhatsApp: Sent price drop notification to ${successCount}/${subscribers.length} subscribers`)
      return successCount > 0
    } catch (error) {
      console.error('Failed to send WhatsApp price drop notification:', error)
      return false
    }
  }

  async sendDailyDeals(products: Product[]): Promise<boolean> {
    try {
      const subscribers = await this.getSubscribers()
      if (subscribers.length === 0) return false

      const message = this.formatDailyDealsMessage(products)
      let successCount = 0

      for (const subscriber of subscribers) {
        const result = await this.sendMessage(subscriber.phone_number, message)
        if (result.success) {
          successCount++
        }
        
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      console.log(`WhatsApp: Sent daily deals to ${successCount}/${subscribers.length} subscribers`)
      return successCount > 0
    } catch (error) {
      console.error('Failed to send WhatsApp daily deals:', error)
      return false
    }
  }

  private formatProductMessage(product: Product, type: 'new' | 'price_drop', extra?: any): string {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    
    if (type === 'new') {
      return `🛍️ *Amazing New Deal!*

*${product.name}*

💰 Price: ₹${product.price?.toLocaleString() || 'N/A'}${product.mrp && product.mrp > (product.price || 0) ? `\n💸 MRP: ₹${product.mrp.toLocaleString()}` : ''}${product.discountPercentage ? `\n🎯 Discount: ${product.discountPercentage}% OFF` : ''}

🔗 Buy Now: ${product.amazonAffiliateLink}

🌟 *Amazing Deals* - Your trusted shopping companion

Reply STOP to unsubscribe`
    } else if (type === 'price_drop') {
      return `📉 *Price Drop Alert!*

*${product.name}*

💸 New Price: ₹${product.price?.toLocaleString() || 'N/A'}
💵 You Save: ₹${extra.savings.toLocaleString()}

🔗 Buy Now: ${product.amazonAffiliateLink}

🌟 *Amazing Deals* - Your trusted shopping companion

Reply STOP to unsubscribe`
    }

    return ''
  }

  private formatDailyDealsMessage(products: Product[]): string {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    
    let message = `🌟 *Today's Amazing Deals!*\n\n`
    
    products.slice(0, 5).forEach((product, index) => {
      message += `${index + 1}. *${product.name}*\n`
      message += `💰 ₹${product.price?.toLocaleString() || 'N/A'}`
      if (product.discountPercentage) {
        message += ` (${product.discountPercentage}% OFF)`
      }
      message += `\n🔗 ${product.amazonAffiliateLink}\n\n`
    })
    
    message += `Visit our store for more deals: ${baseUrl}\n\n`
    message += `🌟 *Amazing Deals* - Your trusted shopping companion\n\n`
    message += `Reply STOP to unsubscribe`
    
    return message
  }

  private async logMessage(messageId: string | null, phoneNumber: string, messageType: string, content: string, status: string, campaignId?: string): Promise<void> {
    try {
      await query(
        'INSERT INTO whatsapp_message_logs (message_id, phone_number, message_type, content, status, campaign_id) VALUES ($1, $2, $3, $4, $5, $6)',
        [messageId, phoneNumber, messageType, content, status, campaignId]
      )
    } catch (error) {
      console.error('Failed to log WhatsApp message:', error)
    }
  }

  async getTemplates(): Promise<WhatsAppTemplate[]> {
    try {
      const result = await query('SELECT * FROM whatsapp_templates ORDER BY created_at DESC')
      return result.rows
    } catch (error) {
      console.error('Failed to get WhatsApp templates:', error)
      return []
    }
  }

  async getCampaigns(): Promise<WhatsAppCampaign[]> {
    try {
      const result = await query('SELECT * FROM whatsapp_campaigns ORDER BY created_at DESC')
      return result.rows
    } catch (error) {
      console.error('Failed to get WhatsApp campaigns:', error)
      return []
    }
  }

  async updateSettings(settings: Partial<WhatsAppSettings>): Promise<boolean> {
    try {
      const updateFields = []
      const values = []
      let paramIndex = 1

      for (const [key, value] of Object.entries(settings)) {
        if (value !== undefined) {
          updateFields.push(`${key} = $${paramIndex}`)
          values.push(value)
          paramIndex++
        }
      }

      if (updateFields.length === 0) {
        return false
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP')

      await query(
        `UPDATE whatsapp_settings SET ${updateFields.join(', ')} WHERE id = (SELECT id FROM whatsapp_settings LIMIT 1)`,
        values
      )

      return true
    } catch (error) {
      console.error('Failed to update WhatsApp settings:', error)
      return false
    }
  }

  getConfig(): Partial<WhatsAppSettings> {
    return {
      is_active: this.settings?.is_active || false,
      business_account_id: this.settings?.business_account_id ? 
        this.settings.business_account_id.substring(0, 10) + '...' : 'Not set',
      phone_number_id: this.settings?.phone_number_id ? 
        this.settings.phone_number_id.substring(0, 10) + '...' : 'Not set'
    }
  }
}

export const whatsappService = new WhatsAppService()
export type { WhatsAppSubscriber, WhatsAppTemplate, WhatsAppCampaign, WhatsAppSettings }
